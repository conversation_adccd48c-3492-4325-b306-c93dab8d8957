@extends('owner.layouts.app')

@section('title', 'Pricing Management')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-tags mr-2"></i>Pricing Management</h1>
            <p class="text-muted mb-0">Dynamic pricing strategies, A/B testing, and revenue optimization</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.pricing.experiments') }}" class="btn btn-outline-primary">
                <i class="fas fa-flask"></i> A/B Tests
            </a>
            <a href="{{ route('owner.pricing.calculator') }}" class="btn btn-outline-info">
                <i class="fas fa-calculator"></i> Price Calculator
            </a>
            <button class="btn btn-primary" data-toggle="modal" data-target="#newStrategyModal">
                <i class="fas fa-plus"></i> New Strategy
            </button>
        </div>
    </div>
@stop

@section('content')
<!-- Pricing Overview Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $pricingMetrics['active_strategies'] }}</h3>
                        <p class="mb-0">Active Strategies</p>
                    </div>
                    <i class="fas fa-cogs fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $pricingMetrics['services_covered'] }} services covered
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($pricingMetrics['average_price'], 2) }}</h3>
                        <p class="mb-0">Average Price</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Range: ${{ number_format($pricingMetrics['min_price'], 2) }} - ${{ number_format($pricingMetrics['max_price'], 2) }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ $pricingMetrics['running_experiments'] }}</h3>
                        <p class="mb-0">Active Tests</p>
                    </div>
                    <i class="fas fa-flask fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $pricingMetrics['completed_experiments'] }} completed
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">{{ number_format($pricingMetrics['revenue_impact'], 1) }}%</h3>
                        <p class="mb-0">Revenue Impact</p>
                    </div>
                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        From optimization
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Strategies & Quick Actions -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Pricing Strategies</h3>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary strategy-filter active" data-type="all">All</button>
                        <button type="button" class="btn btn-outline-primary strategy-filter" data-type="dynamic">Dynamic</button>
                        <button type="button" class="btn btn-outline-primary strategy-filter" data-type="seasonal">Seasonal</button>
                        <button type="button" class="btn btn-outline-primary strategy-filter" data-type="demand">Demand-Based</button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover" id="strategiesTable">
                        <thead>
                            <tr>
                                <th>Strategy</th>
                                <th>Service</th>
                                <th>Type</th>
                                <th class="text-center">Price Range</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Performance</th>
                                <th class="text-right">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($pricingStrategies as $strategy)
                            <tr data-strategy-type="{{ $strategy->strategy_type }}">
                                <td>
                                    <strong>{{ $strategy->strategy_name }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        Created {{ $strategy->created_at->diffForHumans() }}
                                    </small>
                                </td>
                                <td>
                                    <span class="text-primary">
                                        {{ $strategy->service->name ?? 'All Services' }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge badge-{{ $strategy->strategy_type == 'dynamic' ? 'success' : ($strategy->strategy_type == 'seasonal' ? 'warning' : 'info') }}">
                                        {{ ucfirst(str_replace('_', ' ', $strategy->strategy_type)) }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    ${{ number_format($strategy->minimum_price ?? $strategy->base_price, 2) }}
                                    -
                                    ${{ number_format($strategy->maximum_price ?? $strategy->base_price, 2) }}
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-{{ $strategy->is_active ? 'success' : 'secondary' }}">
                                        {{ $strategy->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="text-center">
                                    @php
                                        $performance = $strategy->performance_metrics ?? collect();
                                        $conversionRate = $performance['conversion_rate'] ?? 0;
                                    @endphp
                                    <span class="badge badge-{{ $conversionRate >= 15 ? 'success' : ($conversionRate >= 10 ? 'warning' : 'danger') }}">
                                        {{ number_format($conversionRate, 1) }}%
                                    </span>
                                </td>
                                <td class="text-right">
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary btn-sm view-strategy"
                                                data-strategy-id="{{ $strategy->id }}"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm edit-strategy"
                                                data-strategy-id="{{ $strategy->id }}"
                                                title="Edit Strategy">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-success btn-sm test-strategy"
                                                data-strategy-id="{{ $strategy->id }}"
                                                title="A/B Test">
                                            <i class="fas fa-flask"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center text-muted py-4">
                                    <i class="fas fa-tags fa-2x mb-2"></i><br>
                                    No pricing strategies found.<br>
                                    <button class="btn btn-primary btn-sm mt-2" data-toggle="modal" data-target="#newStrategyModal">
                                        Create Your First Strategy
                                    </button>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Pricing Intelligence</h3>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Optimization Recommendations</h6>
                    @forelse($optimizationRecommendations as $recommendation)
                    <div class="alert alert-info alert-sm py-2 mb-2">
                        <i class="fas fa-lightbulb mr-1"></i>
                        <small>{{ $recommendation['message'] }}</small>
                        @if(isset($recommendation['potential_increase']))
                        <br>
                        <small class="text-success">
                            <strong>+{{ $recommendation['potential_increase'] }}% potential revenue</strong>
                        </small>
                        @endif
                    </div>
                    @empty
                    <p class="text-muted small">No recommendations available yet.</p>
                    @endforelse
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Market Insights</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <span class="text-success h6">${{ number_format($marketInsights['competitive_average'], 2) }}</span><br>
                            <small class="text-muted">Market Average</small>
                        </div>
                        <div class="col-6">
                            <span class="text-primary h6">{{ number_format($marketInsights['demand_score'], 1) }}/10</span><br>
                            <small class="text-muted">Demand Score</small>
                        </div>
                    </div>
                </div>

                <div>
                    <h6 class="text-muted mb-2">Quick Actions</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="runPriceOptimization()">
                            <i class="fas fa-magic"></i> Auto-Optimize Prices
                        </button>
                        <button class="btn btn-outline-info btn-sm" data-toggle="modal" data-target="#bulkPricingModal">
                            <i class="fas fa-edit"></i> Bulk Price Update
                        </button>
                        <a href="{{ route('owner.pricing.calculator') }}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-calculator"></i> Price Calculator
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pricing Performance Charts -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Price vs Revenue Impact</h3>
            </div>
            <div class="card-body">
                <canvas id="priceRevenueChart" height="150"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Demand & Pricing Trends</h3>
            </div>
            <div class="card-body">
                <canvas id="demandTrendsChart" height="150"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Active A/B Tests -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="card-title">Active A/B Tests</h3>
            <a href="{{ route('owner.pricing.experiments') }}" class="btn btn-outline-primary btn-sm">
                View All Tests
            </a>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Test Name</th>
                        <th>Service</th>
                        <th class="text-center">Control Price</th>
                        <th class="text-center">Variant Price</th>
                        <th class="text-center">Progress</th>
                        <th class="text-center">Significance</th>
                        <th class="text-right">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($activeExperiments as $experiment)
                    <tr>
                        <td>
                            <strong>{{ $experiment->experiment_name }}</strong>
                            <br>
                            <small class="text-muted">
                                {{ $experiment->start_date->diffForHumans() }}
                            </small>
                        </td>
                        <td>{{ $experiment->service->name }}</td>
                        <td class="text-center">
                            <span class="badge badge-secondary">
                                ${{ number_format($experiment->control_price, 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge badge-primary">
                                ${{ number_format($experiment->variant_price, 2) }}
                            </span>
                        </td>
                        <td class="text-center">
                            @php
                                $currentSample = ($experiment->control_views + $experiment->variant_views);
                                $progress = $experiment->sample_size > 0 ? ($currentSample / $experiment->sample_size) * 100 : 0;
                            @endphp
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" style="width: {{ min($progress, 100) }}%"></div>
                            </div>
                            <small>{{ number_format(min($progress, 100), 0) }}%</small>
                        </td>
                        <td class="text-center">
                            @if($experiment->statistical_significance >= 0.95)
                                <span class="badge badge-success">
                                    {{ number_format($experiment->statistical_significance * 100, 1) }}%
                                </span>
                            @elseif($experiment->statistical_significance >= 0.80)
                                <span class="badge badge-warning">
                                    {{ number_format($experiment->statistical_significance * 100, 1) }}%
                                </span>
                            @else
                                <span class="badge badge-secondary">
                                    {{ number_format($experiment->statistical_significance * 100, 1) }}%
                                </span>
                            @endif
                        </td>
                        <td class="text-right">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info btn-sm view-experiment"
                                        data-experiment-id="{{ $experiment->id }}">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                                @if($experiment->statistical_significance >= 0.95)
                                <button class="btn btn-outline-success btn-sm conclude-experiment"
                                        data-experiment-id="{{ $experiment->id }}">
                                    <i class="fas fa-check"></i>
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="text-center text-muted py-3">
                            No active A/B tests. <a href="{{ route('owner.pricing.experiments') }}">Start your first test</a>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modals -->
@include('owner.pricing.modals.new-strategy')
@include('owner.pricing.modals.edit-strategy')
@include('owner.pricing.modals.bulk-pricing')
@include('owner.pricing.modals.strategy-details')

@stop

@section('css')
<style>
.opacity-75 {
    opacity: 0.75;
}

.btn-group .btn.active {
    background-color: #007bff;
    color: white;
}

.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

.d-grid {
    display: grid;
}

.gap-2 {
    gap: 0.5rem;
}

.progress {
    background-color: #e9ecef;
}

.card-body .row {
    margin-left: -5px;
    margin-right: -5px;
}

.card-body .row > div {
    padding-left: 5px;
    padding-right: 5px;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize charts
    initializePriceRevenueChart();
    initializeDemandTrendsChart();

    // Strategy filtering
    $('.strategy-filter').on('click', function() {
        $('.strategy-filter').removeClass('active');
        $(this).addClass('active');

        var filterType = $(this).data('type');
        filterStrategies(filterType);
    });

    // Strategy actions
    $('.view-strategy').on('click', function() {
        var strategyId = $(this).data('strategy-id');
        viewStrategyDetails(strategyId);
    });

    $('.edit-strategy').on('click', function() {
        var strategyId = $(this).data('strategy-id');
        editStrategy(strategyId);
    });

    $('.test-strategy').on('click', function() {
        var strategyId = $(this).data('strategy-id');
        startABTest(strategyId);
    });

    // Experiment actions
    $('.view-experiment').on('click', function() {
        var experimentId = $(this).data('experiment-id');
        viewExperimentDetails(experimentId);
    });

    $('.conclude-experiment').on('click', function() {
        var experimentId = $(this).data('experiment-id');
        concludeExperiment(experimentId);
    });
});

function initializePriceRevenueChart() {
    var ctx = document.getElementById('priceRevenueChart').getContext('2d');

    $.get('/owner/pricing/data', {
        'type': 'price_revenue_correlation'
    }, function(data) {
        new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Services',
                    data: data.map(item => ({
                        x: item.price,
                        y: item.revenue
                    })),
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Price ($)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Revenue ($)'
                        }
                    }
                }
            }
        });
    });
}

function initializeDemandTrendsChart() {
    var ctx = document.getElementById('demandTrendsChart').getContext('2d');

    var demandData = @json($demandTrends);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: demandData.map(item => new Date(item.date).toLocaleDateString()),
            datasets: [
                {
                    label: 'Demand Score',
                    data: demandData.map(item => item.demand_score),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    yAxisID: 'y'
                },
                {
                    label: 'Average Price',
                    data: demandData.map(item => item.average_price),
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Demand Score'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Price ($)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function filterStrategies(type) {
    var rows = $('#strategiesTable tbody tr');

    if (type === 'all') {
        rows.show();
    } else {
        rows.hide();
        rows.filter('[data-strategy-type="' + type + '"]').show();
    }
}

function runPriceOptimization() {
    if (confirm('This will analyze your pricing data and suggest optimizations. Continue?')) {
        $.post('/owner/pricing/optimize', {
            _token: '{{ csrf_token() }}'
        }, function(response) {
            if (response.success) {
                toastr.success(response.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                toastr.error(response.message);
            }
        });
    }
}

function viewStrategyDetails(strategyId) {
    $.get('/owner/pricing/strategies/' + strategyId, function(data) {
        $('#strategyDetailsModal .modal-body').html(data);
        $('#strategyDetailsModal').modal('show');
    });
}

function editStrategy(strategyId) {
    $.get('/owner/pricing/strategies/' + strategyId + '/edit', function(data) {
        $('#editStrategyModal .modal-body').html(data);
        $('#editStrategyModal').modal('show');
    });
}

function startABTest(strategyId) {
    window.location.href = '/owner/pricing/experiments/create?strategy_id=' + strategyId;
}

function viewExperimentDetails(experimentId) {
    window.location.href = '/owner/pricing/experiments/' + experimentId;
}

function concludeExperiment(experimentId) {
    if (confirm('This will conclude the A/B test and apply the winning variant. Continue?')) {
        $.post('/owner/pricing/experiments/' + experimentId + '/conclude', {
            _token: '{{ csrf_token() }}'
        }, function(response) {
            if (response.success) {
                toastr.success(response.message);
                setTimeout(() => location.reload(), 1500);
            } else {
                toastr.error(response.message);
            }
        });
    }
}
</script>
@stop
