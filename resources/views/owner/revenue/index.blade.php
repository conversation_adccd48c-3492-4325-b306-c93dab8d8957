@extends('owner.layouts.app')

@section('title', 'Revenue Analytics')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1><i class="fas fa-chart-line mr-2"></i>Revenue Analytics</h1>
            <p class="text-muted mb-0">Advanced revenue intelligence and business insights</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('owner.revenue.analytics') }}" class="btn btn-outline-primary">
                <i class="fas fa-microscope"></i> Advanced Analytics
            </a>
            <a href="{{ route('owner.revenue.export') }}" class="btn btn-primary">
                <i class="fas fa-download"></i> Export Data
            </a>
        </div>
    </div>
@stop

@section('content')
<!-- Date Range Filter -->
<div class="card">
    <div class="card-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h3 class="card-title mb-0">
                    <i class="fas fa-filter mr-2"></i>
                    Revenue Overview
                </h3>
            </div>
            <div class="col-md-6">
                <form method="GET" class="d-flex justify-content-end">
                    <div class="input-group" style="width: 300px;">
                        <select name="period" class="form-control" onchange="this.form.submit()">
                            <option value="today" {{ request('period') == 'today' ? 'selected' : '' }}>Today</option>
                            <option value="yesterday" {{ request('period') == 'yesterday' ? 'selected' : '' }}>Yesterday</option>
                            <option value="week" {{ request('period') == 'week' ? 'selected' : '' }}>This Week</option>
                            <option value="month" {{ request('period') == 'month' || !request('period') ? 'selected' : '' }}>This Month</option>
                            <option value="quarter" {{ request('period') == 'quarter' ? 'selected' : '' }}>This Quarter</option>
                            <option value="year" {{ request('period') == 'year' ? 'selected' : '' }}>This Year</option>
                        </select>
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Metrics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($revenueMetrics['total_revenue'], 2) }}</h3>
                        <p class="mb-0">Total Revenue</p>
                    </div>
                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $revenueMetrics['total_transactions'] }} transactions
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($revenueMetrics['average_revenue_per_customer'], 2) }}</h3>
                        <p class="mb-0">Avg per Customer</p>
                    </div>
                    <i class="fas fa-user-plus fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $revenueMetrics['unique_customers'] }} unique customers
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($revenueMetrics['average_transaction_value'], 2) }}</h3>
                        <p class="mb-0">Avg Transaction</p>
                    </div>
                    <i class="fas fa-receipt fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        {{ $revenueMetrics['refund_rate'] }}% refund rate
                    </small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6">
        <div class="card bg-gradient-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-0">${{ number_format($revenueMetrics['total_fees'], 2) }}</h3>
                        <p class="mb-0">Total Fees</p>
                    </div>
                    <i class="fas fa-percentage fa-2x opacity-75"></i>
                </div>
                <div class="mt-2">
                    <small class="opacity-75">
                        Processing costs
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Trends Chart -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Revenue Trends</h3>
                <div class="card-tools">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-primary chart-period" data-period="daily">Daily</button>
                        <button type="button" class="btn btn-outline-primary chart-period active" data-period="weekly">Weekly</button>
                        <button type="button" class="btn btn-outline-primary chart-period" data-period="monthly">Monthly</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueTrendsChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Revenue Growth</h3>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="revenueGrowthChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Service Performance & Customer Insights -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Top Services by Revenue</h3>
                    <a href="{{ route('owner.revenue.service-performance') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Service</th>
                                <th class="text-center">Bookings</th>
                                <th class="text-right">Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($serviceRevenue->take(5) as $service)
                            <tr>
                                <td>
                                    <strong>{{ $service->service->name ?? 'N/A' }}</strong>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-primary">{{ $service->transaction_count }}</span>
                                </td>
                                <td class="text-right">
                                    <strong>${{ number_format($service->total_revenue, 2) }}</strong>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="text-center text-muted py-3">
                                    No service data available
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Top Customers</h3>
                    <a href="{{ route('owner.revenue.customer-insights') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th class="text-center">Visits</th>
                                <th class="text-right">Spent</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($topCustomers->take(5) as $customer)
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar bg-primary rounded-circle d-flex align-items-center justify-content-center mr-2" style="width: 30px; height: 30px;">
                                            <span class="text-white font-weight-bold">
                                                {{ substr($customer->customer->name ?? 'N/A', 0, 1) }}
                                            </span>
                                        </div>
                                        <span>{{ $customer->customer->name ?? 'N/A' }}</span>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge badge-info">{{ $customer->visit_count }}</span>
                                </td>
                                <td class="text-right">
                                    <strong>${{ number_format($customer->total_spent, 2) }}</strong>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="text-center text-muted py-3">
                                    No customer data available
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Methods & Monthly Performance -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Payment Method Breakdown</h3>
            </div>
            <div class="card-body">
                <canvas id="paymentMethodChart" height="120"></canvas>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Monthly Performance</h3>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th class="text-right">Revenue</th>
                                <th class="text-center">Growth</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($revenueGrowth->take(6) as $month)
                            <tr>
                                <td>{{ \Carbon\Carbon::parse($month['period'])->format('M Y') }}</td>
                                <td class="text-right">
                                    <strong>${{ number_format($month['revenue'], 2) }}</strong>
                                </td>
                                <td class="text-center">
                                    @if($month['growth_rate'] > 0)
                                        <span class="badge badge-success">
                                            <i class="fas fa-arrow-up"></i> {{ $month['growth_rate'] }}%
                                        </span>
                                    @elseif($month['growth_rate'] < 0)
                                        <span class="badge badge-danger">
                                            <i class="fas fa-arrow-down"></i> {{ abs($month['growth_rate']) }}%
                                        </span>
                                    @else
                                        <span class="badge badge-secondary">0%</span>
                                    @endif
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Quick Actions</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.analytics') }}" class="btn btn-outline-primary btn-block">
                    <i class="fas fa-microscope"></i><br>
                    Advanced Analytics
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.service-performance') }}" class="btn btn-outline-info btn-block">
                    <i class="fas fa-chart-bar"></i><br>
                    Service Performance
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.customer-insights') }}" class="btn btn-outline-success btn-block">
                    <i class="fas fa-users"></i><br>
                    Customer Insights
                </a>
            </div>
            <div class="col-md-3">
                <a href="{{ route('owner.revenue.export') }}" class="btn btn-outline-warning btn-block">
                    <i class="fas fa-download"></i><br>
                    Export Reports
                </a>
            </div>
        </div>
    </div>
</div>
@stop

@section('css')
<style>
.opacity-75 {
    opacity: 0.75;
}

.user-avatar {
    font-size: 12px;
}

.chart-container {
    position: relative;
    height: 200px;
}

.btn-group .btn.active {
    background-color: #007bff;
    color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize charts
    initializeRevenueTrendsChart();
    initializeRevenueGrowthChart();
    initializePaymentMethodChart();

    // Chart period switcher
    $('.chart-period').on('click', function() {
        $('.chart-period').removeClass('active');
        $(this).addClass('active');

        var period = $(this).data('period');
        updateRevenueTrendsChart(period);
    });
});

function initializeRevenueTrendsChart() {
    var ctx = document.getElementById('revenueTrendsChart').getContext('2d');

    // Fetch chart data via AJAX
    $.get('/owner/revenue/data', {
        'type': 'weekly',
        'period': '{{ request("period", "month") }}'
    }, function(data) {
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => new Date(item.revenue_date || item.period).toLocaleDateString()),
                datasets: [{
                    label: 'Revenue',
                    data: data.map(item => item.daily_revenue || item.revenue),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Revenue ($)'
                        }
                    }
                }
            }
        });
    });
}

function initializeRevenueGrowthChart() {
    var ctx = document.getElementById('revenueGrowthChart').getContext('2d');

    var growthData = @json($revenueGrowth);

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: growthData.map(item => new Date(item.period).toLocaleDateString('en-US', {month: 'short'})),
            datasets: [{
                label: 'Growth Rate',
                data: growthData.map(item => item.growth_rate),
                backgroundColor: growthData.map(item => item.growth_rate >= 0 ? '#28a745' : '#dc3545'),
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    title: {
                        display: true,
                        text: 'Growth %'
                    }
                }
            }
        }
    });
}

function initializePaymentMethodChart() {
    var ctx = document.getElementById('paymentMethodChart').getContext('2d');

    var paymentData = @json($paymentMethodBreakdown);

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: paymentData.map(item => item.payment_method),
            datasets: [{
                data: paymentData.map(item => item.revenue),
                backgroundColor: [
                    '#007bff',
                    '#28a745',
                    '#ffc107',
                    '#dc3545',
                    '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateRevenueTrendsChart(period) {
    // Update chart based on period selection
    $.get('/owner/revenue/data', {
        'type': period,
        'period': '{{ request("period", "month") }}'
    }, function(data) {
        // Update chart data
        // Implementation would refresh the chart with new data
    });
}
</script>
@stop
