<?php

use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\ProfileController as AdminProfileController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\BusinessController;
use App\Http\Controllers\Admin\BusinessCategoryController;
use App\Http\Controllers\Admin\BusinessManagementController;
use App\Http\Controllers\Admin\BusinessSettingsController;
use App\Http\Controllers\Admin\ServiceController;
use App\Http\Controllers\Admin\ServiceCategoryController;
use App\Http\Controllers\Admin\BookingController;
use App\Http\Controllers\Admin\CalendarController;
use App\Http\Controllers\Admin\ResourceController;
use App\Http\Controllers\Admin\ResourceTypeController;
use App\Http\Controllers\Admin\WaitingListController;
use App\Http\Controllers\Admin\NotificationController;
use App\Http\Controllers\Admin\ReportController;
use App\Http\Controllers\Admin\SettingsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\Owner\DashboardController as OwnerDashboardController;
use App\Http\Controllers\Owner\BusinessController as OwnerBusinessController;
use App\Http\Controllers\Owner\CalendarController as OwnerCalendarController;
use App\Http\Controllers\Owner\ServiceController as OwnerServiceController;
use App\Http\Controllers\Owner\ServiceCategoryController as OwnerServiceCategoryController;
use App\Http\Controllers\Owner\ResourceController as OwnerResourceController;
use App\Http\Controllers\Owner\ScheduleController as OwnerScheduleController;
use App\Http\Controllers\Owner\ResourceTypeController as OwnerResourceTypeController;
use App\Http\Controllers\Owner\ResourceAvailabilityController as OwnerResourceAvailabilityController;
use App\Http\Controllers\Owner\ReviewController as OwnerReviewController;
use App\Http\Controllers\Owner\GalleryController as OwnerGalleryController;
use App\Http\Controllers\Owner\GalleryCategoryController as OwnerGalleryCategoryController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Admin routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'hierarchical.role:Admin'])->group(function () { // 'verified' temporarily disabled for development
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Profile routes
    Route::get('/profile', [AdminProfileController::class, 'index'])->name('profile.index');
    Route::put('/profile', [AdminProfileController::class, 'update'])->name('profile.update');
    Route::get('/password', [AdminProfileController::class, 'showPasswordForm'])->name('profile.password');
    Route::put('/password', [AdminProfileController::class, 'updatePassword'])->name('profile.update-password');

    // User management
    Route::resource('users', UserController::class)->middleware('permission:manage users');

    // Two-Factor Authentication routes
    Route::get('/2fa/verify/{action?}', [App\Http\Controllers\Admin\TwoFactorAuthController::class, 'showVerificationForm'])->name('2fa.verify');
    Route::post('/2fa/verify', [App\Http\Controllers\Admin\TwoFactorAuthController::class, 'verify'])->name('2fa.verify');
    Route::post('/2fa/resend', [App\Http\Controllers\Admin\TwoFactorAuthController::class, 'resend'])->name('2fa.resend');
    Route::get('/2fa/cancel', [App\Http\Controllers\Admin\TwoFactorAuthController::class, 'cancel'])->name('2fa.cancel');

    // Two-Factor Authentication Settings
    Route::get('/settings/two-factor', [App\Http\Controllers\Admin\TwoFactorSettingsController::class, 'index'])->name('settings.two-factor');
    Route::put('/settings/two-factor', [App\Http\Controllers\Admin\TwoFactorSettingsController::class, 'update'])->name('settings.two-factor.update');
    Route::post('/settings/two-factor/toggle', [App\Http\Controllers\Admin\TwoFactorSettingsController::class, 'toggle'])->name('settings.two-factor.toggle');
    Route::post('/settings/two-factor/test', [App\Http\Controllers\Admin\TwoFactorSettingsController::class, 'test'])->name('settings.two-factor.test');
    Route::get('/settings/two-factor/status', [App\Http\Controllers\Admin\TwoFactorSettingsController::class, 'status'])->name('settings.two-factor.status');

    // Role management with enhanced security (2FA configurable)
    Route::resource('roles', RoleController::class)->middleware([
        'hierarchical.role:Admin',
        'permission:manage roles'
    ]);

    // Security Dashboard and Monitoring
    Route::prefix('security')->name('security.')->middleware(['hierarchical.role:Admin'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'index'])->name('dashboard');
        Route::get('/alerts', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'alerts'])->name('alerts');
        Route::get('/alerts/{alert}', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'showAlert'])->name('alerts.show');
        Route::post('/alerts/{alert}/resolve', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'resolveAlert'])->name('alerts.resolve');
        Route::post('/alerts/{alert}/false-positive', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'markFalsePositive'])->name('alerts.false-positive');
        Route::get('/audit-logs', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'auditLogs'])->name('audit-logs');
        Route::get('/compliance-reports', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'complianceReports'])->name('compliance-reports');
        Route::post('/compliance-reports/generate', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'generateComplianceReport'])->name('compliance-reports.generate');
        Route::get('/permission-inheritance', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'permissionInheritance'])->name('permission-inheritance');
        Route::get('/permission-tree', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'getPermissionTree'])->name('permission-tree');
        Route::get('/data-retention', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'dataRetention'])->name('data-retention');
        Route::post('/data-retention/execute', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'executeRetentionPolicies'])->name('retention.execute');
        Route::post('/data-retention/restore', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'restoreFromArchive'])->name('retention.restore');
        Route::post('/scan', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'runSecurityScan'])->name('scan');
        Route::get('/metrics', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'getSecurityMetrics'])->name('metrics');
        Route::get('/trends', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'getSecurityTrends'])->name('trends');
        Route::get('/export', [App\Http\Controllers\Admin\SecurityDashboardController::class, 'exportSecurityData'])->name('export');
    });

    // Business management
    Route::get('/businesses/wizard', [BusinessController::class, 'wizard'])->name('businesses.wizard');
    Route::post('/businesses/wizard', [BusinessController::class, 'storeWizard'])->name('businesses.wizard.store');
    Route::resource('businesses', BusinessController::class)->middleware('permission:manage businesses');
    Route::post('/businesses/{business}/duplicate', [BusinessController::class, 'duplicate'])->name('businesses.duplicate');

    // Business Categories
    Route::resource('business-categories', BusinessCategoryController::class)->middleware('permission:manage businesses');
    Route::post('/business-categories/{businessCategory}/duplicate', [BusinessCategoryController::class, 'duplicate'])->name('business-categories.duplicate');
    Route::post('/business-categories/{businessCategory}/toggle-status', [BusinessCategoryController::class, 'toggleStatus'])->name('business-categories.toggle-status');

    // Business Operating Hours
    Route::get('/businesses/{business}/operating-hours', [App\Http\Controllers\Admin\BusinessOperatingHoursController::class, 'index'])->name('businesses.operating-hours.index');
    Route::put('/businesses/{business}/operating-hours', [App\Http\Controllers\Admin\BusinessOperatingHoursController::class, 'update'])->name('businesses.operating-hours.update');
    Route::post('/businesses/{business}/operating-hours/copy', [App\Http\Controllers\Admin\BusinessOperatingHoursController::class, 'copy'])->name('businesses.operating-hours.copy');
    Route::post('/businesses/{business}/operating-hours/template', [App\Http\Controllers\Admin\BusinessOperatingHoursController::class, 'setTemplate'])->name('businesses.operating-hours.set-template');

    // Business Holidays
    Route::resource('businesses.holidays', App\Http\Controllers\Admin\BusinessHolidaysController::class)->except(['index']);
    Route::get('/businesses/{business}/holidays', [App\Http\Controllers\Admin\BusinessHolidaysController::class, 'index'])->name('businesses.holidays.index');
    Route::post('/businesses/{business}/holidays/import-common', [App\Http\Controllers\Admin\BusinessHolidaysController::class, 'importCommon'])->name('businesses.holidays.import-common');
    Route::get('/businesses/{business}/holidays/calendar', [App\Http\Controllers\Admin\BusinessHolidaysController::class, 'getCalendarHolidays'])->name('businesses.holidays.calendar');

    // Business Locations
    Route::resource('businesses.locations', App\Http\Controllers\Admin\BusinessLocationController::class)->except(['index']);
    Route::get('/businesses/{business}/locations', [App\Http\Controllers\Admin\BusinessLocationController::class, 'index'])->name('businesses.locations.index');
    Route::get('/businesses/{business}/locations/map-data', [App\Http\Controllers\Admin\BusinessLocationController::class, 'getMapData'])->name('businesses.locations.map-data');
    Route::get('/places/search', [App\Http\Controllers\Admin\BusinessLocationController::class, 'searchPlaces'])->name('places.search');
    Route::get('/places/details', [App\Http\Controllers\Admin\BusinessLocationController::class, 'getPlaceDetails'])->name('places.details');

    // General Business Management Pages (with business selection)
    Route::get('/business-management/operating-hours', [BusinessManagementController::class, 'operatingHours'])->name('business-management.operating-hours')->middleware('permission:manage businesses');
    Route::get('/business-management/holidays', [BusinessManagementController::class, 'holidays'])->name('business-management.holidays')->middleware('permission:manage businesses');
    Route::get('/business-management/locations', [BusinessManagementController::class, 'locations'])->name('business-management.locations')->middleware('permission:manage businesses');

    // Service management
    Route::resource('services', ServiceController::class)->middleware('permission:manage services');
    Route::post('/services/{service}/duplicate', [ServiceController::class, 'duplicate'])->name('services.duplicate');

    // Service Category management
    Route::resource('service-categories', ServiceCategoryController::class)->middleware('permission:manage services');
    Route::post('/service-categories/{serviceCategory}/duplicate', [ServiceCategoryController::class, 'duplicate'])->name('service-categories.duplicate');
    Route::post('/service-categories/{serviceCategory}/toggle-status', [ServiceCategoryController::class, 'toggleStatus'])->name('service-categories.toggle-status');

    // Resource management
    Route::resource('resources', ResourceController::class)->middleware('permission:manage resources');
    Route::post('/resources/{resource}/duplicate', [ResourceController::class, 'duplicate'])->name('resources.duplicate');
    Route::get('/businesses/{business}/resources', [ResourceController::class, 'getByBusiness'])->name('businesses.resources');
    Route::post('/resources/{resource}/check-availability', [ResourceController::class, 'checkAvailability'])->name('resources.check-availability');

    // Resource Type management
    Route::resource('resource-types', ResourceTypeController::class)->middleware('permission:manage resources');

    // Waiting list management
    Route::resource('waiting-lists', WaitingListController::class)->middleware('permission:manage waiting lists');
    Route::post('/waiting-lists/{waitingList}/notify', [WaitingListController::class, 'notify'])->name('waiting-lists.notify');
    Route::post('/waiting-lists/{waitingList}/convert-to-booking', [WaitingListController::class, 'convertToBooking'])->name('waiting-lists.convert-to-booking');
    Route::post('/waiting-lists/find-matches', [WaitingListController::class, 'findMatches'])->name('waiting-lists.find-matches');

    // Booking management
    Route::resource('bookings', BookingController::class)->middleware('permission:manage bookings');
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::post('/bookings/{booking}/add-payment', [BookingController::class, 'addPayment'])->name('bookings.add-payment');

    // Check-in management
    Route::get('/check-in', [App\Http\Controllers\Admin\CheckInController::class, 'index'])->name('check-in.index')->middleware('permission:manage bookings');
    Route::post('/bookings/{booking}/check-in', [App\Http\Controllers\Admin\CheckInController::class, 'checkIn'])->name('bookings.check-in')->middleware('permission:manage bookings');
    Route::post('/bookings/{booking}/check-out', [App\Http\Controllers\Admin\CheckInController::class, 'checkOut'])->name('bookings.check-out')->middleware('permission:manage bookings');
    Route::post('/bookings/{booking}/mark-no-show', [App\Http\Controllers\Admin\CheckInController::class, 'markNoShow'])->name('bookings.mark-no-show')->middleware('permission:manage bookings');
    Route::post('/bookings/{booking}/undo-check-in', [App\Http\Controllers\Admin\CheckInController::class, 'undoCheckIn'])->name('bookings.undo-check-in')->middleware('permission:manage bookings');
    Route::get('/check-in/stats', [App\Http\Controllers\Admin\CheckInController::class, 'getStats'])->name('check-in.stats')->middleware('permission:manage bookings');
    Route::get('/check-in/export', [App\Http\Controllers\Admin\CheckInController::class, 'export'])->name('check-in.export')->middleware('permission:manage bookings');

    // Calendar management
    Route::get('/calendar', [CalendarController::class, 'index'])->name('calendar.index');
    Route::get('/calendar/events', [CalendarController::class, 'events'])->name('calendar.events');
    Route::get('/calendar/booking/{bookingId}', [CalendarController::class, 'showBooking'])->name('calendar.booking');
    Route::get('/calendar/block/{blockId}', [CalendarController::class, 'showBlock'])->name('calendar.block');
    Route::post('/calendar/booking', [CalendarController::class, 'createBooking'])->name('calendar.create-booking');
    Route::post('/calendar/quick-booking', [CalendarController::class, 'storeQuickBooking'])->name('calendar.store-quick-booking');
    Route::post('/calendar/block', [CalendarController::class, 'createBlock'])->name('calendar.create-block');

    // Notification management
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index')->middleware('permission:manage notifications');
    Route::get('/notifications/reminders', [NotificationController::class, 'reminders'])->name('notifications.reminders')->middleware('permission:manage notifications');
    Route::post('/bookings/{booking}/send-confirmation', [NotificationController::class, 'sendConfirmation'])->name('notifications.send-confirmation');
    Route::post('/bookings/{booking}/send-reminder', [NotificationController::class, 'sendReminder'])->name('notifications.send-reminder');
    Route::post('/waiting-lists/{waitingList}/send-notification', [NotificationController::class, 'sendWaitingListNotification'])->name('notifications.send-waiting-list');
    Route::post('/notifications/process-due-reminders', [NotificationController::class, 'processDueReminders'])->name('notifications.process-due-reminders');
    Route::post('/reminders/{reminder}/cancel', [NotificationController::class, 'cancelReminder'])->name('notifications.cancel-reminder');
    Route::post('/reminders/{reminder}/resend', [NotificationController::class, 'resendReminder'])->name('notifications.resend-reminder');
    Route::get('/notifications/stats', [NotificationController::class, 'getStats'])->name('notifications.stats');
    Route::post('/notifications/test', [NotificationController::class, 'testNotification'])->name('notifications.test');

    // Reports management
    Route::prefix('reports')->name('reports.')->middleware('permission:view reports')->group(function () {
        Route::get('/booking-reports', [App\Http\Controllers\Admin\ReportController::class, 'bookingReports'])->name('booking-reports');
        Route::get('/revenue-reports', [App\Http\Controllers\Admin\ReportController::class, 'revenueReports'])->name('revenue-reports');
        Route::get('/customer-reports', [App\Http\Controllers\Admin\ReportController::class, 'customerReports'])->name('customer-reports');
        Route::get('/resource-utilization', [App\Http\Controllers\Admin\ReportController::class, 'resourceUtilization'])->name('resource-utilization');
        Route::get('/export/{type}', [App\Http\Controllers\Admin\ReportController::class, 'export'])->name('export');
    });

    // Business Settings (must come before catch-all settings routes)
    Route::get('/settings/branding', [BusinessSettingsController::class, 'branding'])->name('settings.branding')->middleware('permission:manage businesses');
    Route::post('/settings/branding', [BusinessSettingsController::class, 'updateBranding'])->name('settings.branding.update')->middleware('permission:manage businesses');
    Route::get('/settings/check-in-rules', [BusinessSettingsController::class, 'checkInRules'])->name('settings.check-in-rules')->middleware('permission:manage bookings');
    Route::post('/settings/check-in-rules', [BusinessSettingsController::class, 'updateCheckInRules'])->name('settings.check-in-rules.update')->middleware('permission:manage bookings');
    Route::get('/settings/no-show', [BusinessSettingsController::class, 'noShowManagement'])->name('settings.no-show')->middleware('permission:manage bookings');
    Route::post('/settings/no-show', [BusinessSettingsController::class, 'updateNoShowManagement'])->name('settings.no-show.update')->middleware('permission:manage bookings');
    Route::get('/settings/email-templates', [BusinessSettingsController::class, 'emailTemplates'])->name('settings.email-templates')->middleware('permission:manage notifications');
    Route::post('/settings/email-templates', [BusinessSettingsController::class, 'updateEmailTemplates'])->name('settings.email-templates.update')->middleware('permission:manage notifications');
    Route::get('/settings/sms', [BusinessSettingsController::class, 'smsSettings'])->name('settings.sms')->middleware('permission:manage notifications');
    Route::post('/settings/sms', [BusinessSettingsController::class, 'updateSmsSettings'])->name('settings.sms.update')->middleware('permission:manage notifications');
    Route::post('/settings/sms/test', [BusinessSettingsController::class, 'sendTestSms'])->name('settings.sms.test')->middleware('permission:manage notifications');
    Route::get('/settings/reminders', [BusinessSettingsController::class, 'reminderRules'])->name('settings.reminders')->middleware('permission:manage notifications');
    Route::post('/settings/reminders', [BusinessSettingsController::class, 'updateReminderRules'])->name('settings.reminders.update')->middleware('permission:manage notifications');
    Route::get('/settings/waiting-list-alerts', [BusinessSettingsController::class, 'waitingListAlerts'])->name('settings.waiting-list-alerts')->middleware('permission:manage notifications');
    Route::post('/settings/waiting-list-alerts', [BusinessSettingsController::class, 'updateWaitingListAlerts'])->name('settings.waiting-list-alerts.update')->middleware('permission:manage notifications');
    Route::get('/settings/email', [BusinessSettingsController::class, 'emailSettings'])->name('settings.email')->middleware('permission:manage notifications');
    Route::post('/settings/email', [BusinessSettingsController::class, 'updateEmailSettings'])->name('settings.email.update')->middleware('permission:manage notifications');
    Route::post('/settings/email/test', [BusinessSettingsController::class, 'sendTestEmail'])->name('settings.email.test')->middleware('permission:manage notifications');

    // Settings management (catch-all routes must come after specific routes)
    Route::get('/settings/{group?}', [SettingsController::class, 'index'])->name('settings.index')->middleware('permission:manage settings');
    Route::post('/settings/{group}', [SettingsController::class, 'update'])->name('settings.update')->middleware('permission:manage settings');
    Route::get('/settings/create/new', [SettingsController::class, 'create'])->name('settings.create')->middleware('permission:manage settings');
    Route::post('/settings', [SettingsController::class, 'store'])->name('settings.store')->middleware('permission:manage settings');
    Route::get('/settings/{setting}/edit', [SettingsController::class, 'edit'])->name('settings.edit')->middleware('permission:manage settings');
    Route::put('/settings/{setting}', [SettingsController::class, 'updateSetting'])->name('settings.update-setting')->middleware('permission:manage settings');
    Route::delete('/settings/{setting}', [SettingsController::class, 'destroy'])->name('settings.destroy')->middleware('permission:manage settings');
});

// Business creation route (before business ownership check)
Route::prefix('owner')->name('owner.')->middleware(['auth', 'configure.owner'])->group(function () { // 'verified' temporarily disabled for development
    Route::get('/business/create', [OwnerBusinessController::class, 'create'])->name('business.create');
    Route::post('/business', [OwnerBusinessController::class, 'store'])->name('business.store');
});

// Owner routes (requires business ownership)
Route::prefix('owner')->name('owner.')->middleware(['auth', 'configure.owner'])->group(function () { // 'verified' temporarily disabled for development
    // Dashboard
    Route::get('/dashboard', [OwnerDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/quick-stats', [OwnerDashboardController::class, 'getQuickStats'])->name('dashboard.quick-stats');

    // Business Management
    Route::get('/business', [OwnerBusinessController::class, 'index'])->name('business.index');
    Route::get('/business/general', [OwnerBusinessController::class, 'general'])->name('business.general');
    Route::put('/business/general', [OwnerBusinessController::class, 'updateGeneral'])->name('business.general.update');
    Route::get('/business/operating-hours', [OwnerBusinessController::class, 'operatingHours'])->name('business.operating-hours');
    Route::put('/business/operating-hours', [OwnerBusinessController::class, 'updateOperatingHours'])->name('business.operating-hours.update');
    Route::get('/business/holidays', [OwnerBusinessController::class, 'holidays'])->name('business.holidays');
    Route::post('/business/holidays', [OwnerBusinessController::class, 'storeHoliday'])->name('business.holidays.store');
    Route::put('/business/holidays/{holiday}', [OwnerBusinessController::class, 'updateHoliday'])->name('business.holidays.update');
    Route::delete('/business/holidays/{holiday}', [OwnerBusinessController::class, 'destroyHoliday'])->name('business.holidays.destroy');
    Route::post('/business/holidays/import-common', [OwnerBusinessController::class, 'importCommonHolidays'])->name('business.holidays.import-common');
    Route::get('/business/locations', [OwnerBusinessController::class, 'locations'])->name('business.locations');
    Route::post('/business/locations', [OwnerBusinessController::class, 'storeLocation'])->name('business.locations.store');
    Route::put('/business/locations/{location}', [OwnerBusinessController::class, 'updateLocation'])->name('business.locations.update');
    Route::delete('/business/locations/{location}', [OwnerBusinessController::class, 'destroyLocation'])->name('business.locations.destroy');
    Route::post('/business/locations/{location}/set-primary', [OwnerBusinessController::class, 'setPrimaryLocation'])->name('business.locations.set-primary');
    Route::get('/business/locations/{location}/operating-hours', [OwnerBusinessController::class, 'locationOperatingHours'])->name('business.locations.operating-hours');
    Route::put('/business/locations/{location}/operating-hours', [OwnerBusinessController::class, 'updateLocationOperatingHours'])->name('business.locations.operating-hours.update');
    Route::post('/business/locations/{location}/copy-business-hours', [OwnerBusinessController::class, 'copyBusinessHours'])->name('business.locations.copy-business-hours');
    Route::get('/business/locations/{location}/holidays', [OwnerBusinessController::class, 'locationHolidays'])->name('business.locations.holidays');
    Route::post('/business/locations/{location}/holidays', [OwnerBusinessController::class, 'storeLocationHoliday'])->name('business.locations.holidays.store');
    Route::put('/business/locations/{location}/holidays/{holiday}', [OwnerBusinessController::class, 'updateLocationHoliday'])->name('business.locations.holidays.update');
    Route::delete('/business/locations/{location}/holidays/{holiday}', [OwnerBusinessController::class, 'destroyLocationHoliday'])->name('business.locations.holidays.destroy');
    Route::post('/business/locations/{location}/copy-business-holidays', [OwnerBusinessController::class, 'copyBusinessHolidays'])->name('business.locations.copy-business-holidays');
    Route::post('/business/locations/{location}/import-common-holidays', [OwnerBusinessController::class, 'importCommonLocationHolidays'])->name('business.locations.import-common-holidays');

    // Quick Actions for Locations
    Route::post('/business/locations/import-google', [OwnerBusinessController::class, 'importFromGoogle'])->name('business.locations.import-google');
    Route::post('/business/locations/bulk-update-hours', [OwnerBusinessController::class, 'bulkUpdateHours'])->name('business.locations.bulk-update-hours');
    Route::get('/business/locations/export', [OwnerBusinessController::class, 'exportLocations'])->name('business.locations.export');
    Route::get('/business/branding', [OwnerBusinessController::class, 'branding'])->name('business.branding');
    Route::put('/business/branding', [OwnerBusinessController::class, 'updateBranding'])->name('business.branding.update');

    // Landing Page Management
    Route::prefix('landing-page')->name('landing-page.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'store'])->name('store');
        Route::get('/edit', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'edit'])->name('edit');
        Route::put('/', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'update'])->name('update');
        Route::post('/publish', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'publish'])->name('publish');
        Route::post('/unpublish', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'unpublish'])->name('unpublish');
        Route::get('/preview', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'preview'])->name('preview');
        Route::get('/check-slug', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'checkSlug'])->name('check-slug');

        // Section Management
        Route::prefix('sections')->name('sections.')->group(function () {
            Route::get('/{section}/edit', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'editSection'])->name('edit');
            Route::put('/{section}', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'updateSection'])->name('update');
            Route::get('/{section}/content', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'getSectionContent'])->name('content');
            Route::post('/{section}/upload-image', [App\Http\Controllers\Owner\BusinessLandingPageController::class, 'uploadSectionImage'])->name('upload-image');
        });
    });

    // Theme Customization
    Route::prefix('theme')->name('theme.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'index'])->name('index');
        Route::get('/config/{theme}', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'getThemeConfig'])->name('config');
        Route::post('/preview', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'preview'])->name('preview');
        Route::post('/save', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'save'])->name('save');
        Route::post('/reset', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'reset'])->name('reset');
        Route::get('/export', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'export'])->name('export');
        Route::post('/import', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'import'])->name('import');
        Route::get('/current', [App\Http\Controllers\Owner\ThemeCustomizationController::class, 'getCurrent'])->name('current');
    });

    // Services Management
    Route::resource('services', OwnerServiceController::class);
    Route::post('/services/{service}/toggle-status', [OwnerServiceController::class, 'toggleStatus'])->name('services.toggle-status');
    Route::post('/services/{service}/duplicate', [OwnerServiceController::class, 'duplicate'])->name('services.duplicate');

    // Service Categories Management
    Route::get('/service-categories', [OwnerServiceCategoryController::class, 'index'])->name('service-categories.index');
    Route::post('/service-categories', [OwnerServiceCategoryController::class, 'store'])->name('service-categories.store');
    Route::put('/service-categories/{serviceCategory}', [OwnerServiceCategoryController::class, 'update'])->name('service-categories.update');

    // Service Display Management for Landing Pages
    Route::prefix('service-display')->name('service-display.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'index'])->name('index');
        Route::put('/', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'update'])->name('update');
        Route::put('/service/{service}', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'updateService'])->name('update-service');
        Route::post('/bulk-visibility', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'bulkUpdateVisibility'])->name('bulk-visibility');
        Route::post('/reorder', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'reorderServices'])->name('reorder');
        Route::get('/preview', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'preview'])->name('preview');
        Route::get('/analytics', [App\Http\Controllers\Owner\ServiceDisplayController::class, 'analytics'])->name('analytics');
    });
    Route::delete('/service-categories/{serviceCategory}', [OwnerServiceCategoryController::class, 'destroy'])->name('service-categories.destroy');
    Route::post('/service-categories/{serviceCategory}/toggle-status', [OwnerServiceCategoryController::class, 'toggleStatus'])->name('service-categories.toggle-status');
    Route::post('/service-categories/{serviceCategory}/duplicate', [OwnerServiceCategoryController::class, 'duplicate'])->name('service-categories.duplicate');
    Route::post('/service-categories/update-order', [OwnerServiceCategoryController::class, 'updateOrder'])->name('service-categories.update-order');

    // Resources Management
    Route::resource('resources', OwnerResourceController::class);
    Route::post('/resources/{resource}/duplicate', [OwnerResourceController::class, 'duplicate'])->name('resources.duplicate');
    Route::post('/resources/{resource}/check-availability', [OwnerResourceController::class, 'checkAvailability'])->name('resources.check-availability');
    Route::post('/resources/bulk-activate', [OwnerResourceController::class, 'bulkActivate'])->name('resources.bulk-activate');
    Route::post('/resources/bulk-deactivate', [OwnerResourceController::class, 'bulkDeactivate'])->name('resources.bulk-deactivate');
    Route::post('/resources/bulk-delete', [OwnerResourceController::class, 'bulkDelete'])->name('resources.bulk-delete');

    // Resource Types Management
    Route::get('/resource-types', [OwnerResourceTypeController::class, 'index'])->name('resource-types.index');
    Route::post('/resource-types', [OwnerResourceTypeController::class, 'store'])->name('resource-types.store');
    Route::put('/resource-types/{resourceType}', [OwnerResourceTypeController::class, 'update'])->name('resource-types.update');
    Route::delete('/resource-types/{resourceType}', [OwnerResourceTypeController::class, 'destroy'])->name('resource-types.destroy');
    Route::get('/resource-types/icons', [OwnerResourceTypeController::class, 'getAvailableIcons'])->name('resource-types.icons');
    Route::get('/resource-types/colors', [OwnerResourceTypeController::class, 'getPredefinedColors'])->name('resource-types.colors');
    Route::get('/resource-types/select', [OwnerResourceTypeController::class, 'getForSelect'])->name('resource-types.select');
    Route::get('/resource-types/analytics', [OwnerResourceTypeController::class, 'getUsageAnalytics'])->name('resource-types.analytics');
    Route::get('/resource-types/export', [OwnerResourceTypeController::class, 'export'])->name('resource-types.export');

    // Resource Availability API
    Route::post('/resources/availability/check', [OwnerResourceAvailabilityController::class, 'checkServiceAvailability'])->name('resources.availability.check');
    Route::get('/resources/{resource}/utilization', [OwnerResourceAvailabilityController::class, 'getResourceUtilization'])->name('resources.utilization');
    Route::post('/resources/availability/alternatives', [OwnerResourceAvailabilityController::class, 'findAlternatives'])->name('resources.availability.alternatives');
    Route::post('/resources/availability/next-slot', [OwnerResourceAvailabilityController::class, 'getNextAvailableSlot'])->name('resources.availability.next-slot');
    Route::get('/resources/{resource}/calendar', [OwnerResourceAvailabilityController::class, 'getResourceCalendar'])->name('resources.calendar');

    // Debug routes for development
    Route::get('/debug/resource-test', function() {
        $business = Auth::user()->ownedBusinesses()->active()->first();
        if (!$business) {
            return 'No business found for user';
        }

        $resourceType = \App\Models\ResourceType::first();
        if (!$resourceType) {
            return 'No resource types found';
        }

        try {
            $resource = $business->resources()->create([
                'name' => 'Debug Test Resource ' . now()->format('H:i:s'),
                'resource_type_id' => $resourceType->id,
                'description' => 'Test description',
                'capacity' => 1,
                'specifications' => [],
                'availability_rules' => [
                    'operating_days' => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
                    'available_from' => '09:00',
                    'available_until' => '17:00',
                    'advance_booking_days' => 30,
                    'minimum_notice_hours' => 2,
                ],
                'requires_approval' => false,
                'is_active' => true,
            ]);

            return 'Resource created successfully with ID: ' . $resource->id;
        } catch (\Exception $e) {
            return 'Error creating resource: ' . $e->getMessage() . ' | Trace: ' . $e->getTraceAsString();
        }
    })->name('debug.resource-test');

    // Debug route for form data
    Route::post('/debug/form-data', function(\Illuminate\Http\Request $request) {
        return response()->json([
            'all_data' => $request->all(),
            'files' => $request->allFiles(),
            'headers' => $request->headers->all(),
            'method' => $request->method(),
            'url' => $request->url(),
        ]);
    })->name('debug.form-data');

    // Schedule & Calendar
    Route::get('/schedule/today', [OwnerScheduleController::class, 'today'])->name('schedule.today');
    Route::get('/schedule/today/stats', [OwnerScheduleController::class, 'getStats'])->name('schedule.today.stats');
    Route::get('/schedule/today/appointments', [OwnerScheduleController::class, 'getAppointments'])->name('schedule.today.appointments');
    Route::get('/schedule/today/resources', [OwnerScheduleController::class, 'getResourceStatus'])->name('schedule.today.resources');
    Route::post('/schedule/today/quick-action', [OwnerScheduleController::class, 'quickAction'])->name('schedule.today.quick-action');

    // Calendar management
    Route::get('/calendar', [OwnerCalendarController::class, 'index'])->name('calendar.index');
    Route::get('/calendar/events', [OwnerCalendarController::class, 'events'])->name('calendar.events');
    Route::get('/calendar/booking/{bookingId}', [OwnerCalendarController::class, 'showBooking'])->name('calendar.booking');
    Route::get('/calendar/block/{blockId}', [OwnerCalendarController::class, 'showBlock'])->name('calendar.block');
    Route::post('/calendar/booking', [OwnerCalendarController::class, 'createBooking'])->name('calendar.create-booking');
    Route::put('/calendar/booking/{bookingId}', [OwnerCalendarController::class, 'updateBooking'])->name('calendar.update-booking');
    Route::put('/calendar/booking/{bookingId}/status', [OwnerCalendarController::class, 'updateBookingStatus'])->name('calendar.update-booking-status');
    Route::post('/calendar/block', [OwnerCalendarController::class, 'createBlock'])->name('calendar.create-block');
    Route::put('/calendar/block/{blockId}', [OwnerCalendarController::class, 'updateBlock'])->name('calendar.update-block');
    Route::delete('/calendar/block/{blockId}', [OwnerCalendarController::class, 'deleteBlock'])->name('calendar.delete-block');
    Route::get('/calendar/available-slots', [OwnerCalendarController::class, 'getAvailableSlots'])->name('calendar.available-slots');
    Route::get('/calendar/debug-booking', [OwnerCalendarController::class, 'debugBooking'])->name('calendar.debug-booking');
    Route::get('/calendar/diagnostic', [OwnerCalendarController::class, 'diagnostic'])->name('calendar.diagnostic');
    Route::post('/calendar/test-availability', [OwnerCalendarController::class, 'testAvailability'])->name('calendar.test-availability');
    Route::post('/calendar/recurring-booking', [OwnerCalendarController::class, 'createRecurringBooking'])->name('calendar.create-recurring-booking');
    Route::get('/calendar/export', [OwnerCalendarController::class, 'export'])->name('calendar.export');

    // Bookings Management
    Route::resource('bookings', App\Http\Controllers\Owner\BookingController::class);
    Route::post('/bookings/{booking}/cancel', [App\Http\Controllers\Owner\BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::post('/bookings/{booking}/check-in', [App\Http\Controllers\Owner\BookingController::class, 'checkIn'])->name('bookings.check-in');
    Route::post('/bookings/{booking}/check-out', [App\Http\Controllers\Owner\BookingController::class, 'checkOut'])->name('bookings.check-out');
    Route::post('/bookings/{booking}/no-show', [App\Http\Controllers\Owner\BookingController::class, 'markNoShow'])->name('bookings.no-show');
    Route::patch('/bookings/{booking}/status', [App\Http\Controllers\Owner\BookingController::class, 'updateStatus'])->name('bookings.update-status');
    Route::get('/bookings/{booking}/data', [App\Http\Controllers\Owner\BookingController::class, 'getBookingData'])->name('bookings.data');
    // Check-in Management
    Route::get('/check-in', [App\Http\Controllers\Owner\CheckInController::class, 'index'])->name('check-in.index');
    Route::get('/check-in/stats', [App\Http\Controllers\Owner\CheckInController::class, 'getStats'])->name('check-in.stats');
    Route::post('/check-in/{booking}/check-in', [App\Http\Controllers\Owner\CheckInController::class, 'checkIn'])->name('check-in.check-in');
    Route::post('/check-in/{booking}/check-out', [App\Http\Controllers\Owner\CheckInController::class, 'checkOut'])->name('check-in.check-out');
    Route::post('/check-in/{booking}/no-show', [App\Http\Controllers\Owner\CheckInController::class, 'markNoShow'])->name('check-in.no-show');
    Route::get('/check-in/search', [App\Http\Controllers\Owner\CheckInController::class, 'search'])->name('check-in.search');

    // Waiting Lists Management
    Route::get('/waiting-lists', [App\Http\Controllers\Owner\WaitingListController::class, 'index'])->name('waiting-lists.index');
    Route::post('/waiting-lists', [App\Http\Controllers\Owner\WaitingListController::class, 'store'])->name('waiting-lists.store');
    Route::put('/waiting-lists/{waitingList}', [App\Http\Controllers\Owner\WaitingListController::class, 'update'])->name('waiting-lists.update');
    Route::delete('/waiting-lists/{waitingList}', [App\Http\Controllers\Owner\WaitingListController::class, 'destroy'])->name('waiting-lists.destroy');
    Route::post('/waiting-lists/update-priority', [App\Http\Controllers\Owner\WaitingListController::class, 'updatePriority'])->name('waiting-lists.update-priority');
    Route::get('/waiting-lists/{waitingList}/find-matches', [App\Http\Controllers\Owner\WaitingListController::class, 'findMatches'])->name('waiting-lists.find-matches');
    Route::post('/waiting-lists/{waitingList}/notify', [App\Http\Controllers\Owner\WaitingListController::class, 'notify'])->name('waiting-lists.notify');
    Route::post('/waiting-lists/{waitingList}/convert-to-booking', [App\Http\Controllers\Owner\WaitingListController::class, 'convertToBooking'])->name('waiting-lists.convert-to-booking');
    Route::get('/waiting-lists/stats', [App\Http\Controllers\Owner\WaitingListController::class, 'getStats'])->name('waiting-lists.stats');
    Route::get('/waiting-lists/auto-match', [App\Http\Controllers\Owner\WaitingListController::class, 'autoMatch'])->name('waiting-lists.auto-match');

    // Integration endpoints
    Route::prefix('integration')->name('integration.')->group(function () {
        Route::get('/notifications', [App\Http\Controllers\Owner\IntegrationController::class, 'getNotifications'])->name('notifications');
        Route::get('/dashboard-stats', [App\Http\Controllers\Owner\IntegrationController::class, 'getDashboardStats'])->name('dashboard-stats');
        Route::get('/related-data/{section}/{id}', [App\Http\Controllers\Owner\IntegrationController::class, 'getRelatedData'])->name('related-data');
        Route::post('/booking-status-change', [App\Http\Controllers\Owner\IntegrationController::class, 'handleBookingStatusChange'])->name('booking-status-change');
        Route::get('/quick-actions/{section}', [App\Http\Controllers\Owner\IntegrationController::class, 'getQuickActions'])->name('quick-actions');
        Route::get('/cross-section-summary', [App\Http\Controllers\Owner\IntegrationController::class, 'getCrossSectionSummary'])->name('cross-section-summary');
    });

    // Redirect for common mistake: accessing integration notifications as a page
    Route::get('/integration/notifications', function () {
        return redirect()->route('owner.notifications.index');
    })->name('integration.notifications.redirect');

    // Customer Management
    Route::resource('customers', App\Http\Controllers\Owner\CustomerController::class);
    Route::post('/customers/{customer}/send-communication', [App\Http\Controllers\Owner\CustomerController::class, 'sendCommunication'])->name('customers.send-communication');
    Route::post('/customers/{customer}/award-points', [App\Http\Controllers\Owner\CustomerController::class, 'awardPoints'])->name('customers.award-points');
    Route::post('/customers/{customer}/assign-tag', [App\Http\Controllers\Owner\CustomerController::class, 'assignTag'])->name('customers.assign-tag');
    Route::delete('/customers/{customer}/remove-tag', [App\Http\Controllers\Owner\CustomerController::class, 'removeTag'])->name('customers.remove-tag');
    Route::post('/customers/{customer}/restore', [App\Http\Controllers\Owner\CustomerController::class, 'restore'])->name('customers.restore');
    Route::get('/customers/export', [App\Http\Controllers\Owner\CustomerController::class, 'export'])->name('customers.export');
    Route::post('/customers/import', [App\Http\Controllers\Owner\CustomerController::class, 'import'])->name('customers.import');
    Route::post('/customers/bulk-action', [App\Http\Controllers\Owner\CustomerController::class, 'bulkAction'])->name('customers.bulk-action');
    Route::get('/customers/birthdays', [App\Http\Controllers\Owner\CustomerController::class, 'getBirthdayCustomers'])->name('customers.birthdays');

    // Reviews Management
    Route::resource('reviews', OwnerReviewController::class);
    Route::post('/reviews/{review}/toggle-featured', [OwnerReviewController::class, 'toggleFeatured'])->name('reviews.toggle-featured');
    Route::patch('/reviews/{review}/status', [OwnerReviewController::class, 'updateStatus'])->name('reviews.update-status');

    // Gallery Management
    Route::resource('gallery', OwnerGalleryController::class)->parameters(['gallery' => 'image']);
    Route::post('/gallery/{image}/toggle-featured', [OwnerGalleryController::class, 'toggleFeatured'])->name('gallery.toggle-featured');
    Route::post('/gallery/{image}/toggle-active', [OwnerGalleryController::class, 'toggleActive'])->name('gallery.toggle-active');
    Route::post('/gallery/update-order', [OwnerGalleryController::class, 'updateOrder'])->name('gallery.update-order');
    Route::post('/gallery/bulk-delete', [OwnerGalleryController::class, 'bulkDelete'])->name('gallery.bulk-delete');
    Route::get('/gallery/stats', [OwnerGalleryController::class, 'getStats'])->name('gallery.stats');

    // Gallery Categories
    Route::prefix('gallery')->name('gallery.')->group(function () {
        Route::resource('categories', OwnerGalleryCategoryController::class);
        Route::post('/categories/{category}/toggle-active', [OwnerGalleryCategoryController::class, 'toggleActive'])->name('categories.toggle-active');
    });

    // Staff Management
    Route::resource('staff', App\Http\Controllers\Owner\StaffController::class);
    Route::post('/customers/birthday-wishes', [App\Http\Controllers\Owner\CustomerController::class, 'sendBirthdayWishes'])->name('customers.birthday-wishes');
    Route::get('/customers/{customer}/referrals', [App\Http\Controllers\Owner\CustomerController::class, 'getReferrals'])->name('customers.referrals');
    Route::post('/customers/{customer}/referrals', [App\Http\Controllers\Owner\CustomerController::class, 'addReferral'])->name('customers.add-referral');
    Route::get('/customers/{customer}/activity', [App\Http\Controllers\Owner\CustomerController::class, 'getActivityTimeline'])->name('customers.activity');
    Route::post('/customers/{customer}/activity', [App\Http\Controllers\Owner\CustomerController::class, 'addActivity'])->name('customers.add-activity');
    Route::get('/customers/stats', [App\Http\Controllers\Owner\CustomerController::class, 'getStats'])->name('customers.stats');

    // Customer Tags Management
    Route::get('/customer-tags', [App\Http\Controllers\Owner\CustomerTagController::class, 'index'])->name('customer-tags.index');
    Route::post('/customer-tags', [App\Http\Controllers\Owner\CustomerTagController::class, 'store'])->name('customer-tags.store');
    Route::put('/customer-tags/{tag}', [App\Http\Controllers\Owner\CustomerTagController::class, 'update'])->name('customer-tags.update');
    Route::delete('/customer-tags/{tag}', [App\Http\Controllers\Owner\CustomerTagController::class, 'destroy'])->name('customer-tags.destroy');
    Route::post('/customer-tags/{tag}/toggle-status', [App\Http\Controllers\Owner\CustomerTagController::class, 'toggleStatus'])->name('customer-tags.toggle-status');
    Route::get('/customer-tags/{tag}/stats', [App\Http\Controllers\Owner\CustomerTagController::class, 'getStats'])->name('customer-tags.stats');
    Route::get('/customer-tags/{tag}/export', [App\Http\Controllers\Owner\CustomerTagController::class, 'export'])->name('customer-tags.export');
    Route::post('/customer-tags/{tag}/bulk-assign', [App\Http\Controllers\Owner\CustomerTagController::class, 'bulkAssign'])->name('customer-tags.bulk-assign');
    Route::post('/customer-tags/create-defaults', [App\Http\Controllers\Owner\CustomerTagController::class, 'createDefaults'])->name('customer-tags.create-defaults');

    // Notifications Management
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\NotificationController::class, 'index'])->name('index');
        Route::get('/stats', [App\Http\Controllers\Owner\NotificationController::class, 'getStats'])->name('stats');
        Route::get('/unread-count', [App\Http\Controllers\Owner\NotificationController::class, 'getUnreadCount'])->name('unread-count');
        Route::get('/recent', [App\Http\Controllers\Owner\NotificationController::class, 'getRecent'])->name('recent');
        Route::get('/analytics', [App\Http\Controllers\Owner\NotificationController::class, 'getAnalytics'])->name('analytics');
        Route::get('/analytics-dashboard', [App\Http\Controllers\Owner\NotificationController::class, 'analytics'])->name('analytics-dashboard');
        Route::post('/export-analytics', [App\Http\Controllers\Owner\NotificationController::class, 'exportAnalytics'])->name('export-analytics');
        Route::get('/ai-insights', [App\Http\Controllers\Owner\NotificationController::class, 'getAIInsights'])->name('ai-insights');
        Route::get('/templates', [App\Http\Controllers\Owner\NotificationController::class, 'getTemplates'])->name('templates');
        Route::post('/export', [App\Http\Controllers\Owner\NotificationController::class, 'export'])->name('export');
        Route::post('/create-from-template', [App\Http\Controllers\Owner\NotificationController::class, 'createFromTemplate'])->name('create-from-template');
        Route::get('/{id}', [App\Http\Controllers\Owner\NotificationController::class, 'show'])->name('show');
        Route::post('/{id}/mark-read', [App\Http\Controllers\Owner\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::post('/{id}/mark-unread', [App\Http\Controllers\Owner\NotificationController::class, 'markAsUnread'])->name('mark-unread');
        Route::post('/mark-all-read', [App\Http\Controllers\Owner\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::post('/{id}/delete', [App\Http\Controllers\Owner\NotificationController::class, 'delete'])->name('delete');
        Route::post('/{id}/restore', [App\Http\Controllers\Owner\NotificationController::class, 'restore'])->name('restore');
        Route::delete('/{id}', [App\Http\Controllers\Owner\NotificationController::class, 'destroy'])->name('destroy');
        Route::post('/bulk-action', [App\Http\Controllers\Owner\NotificationController::class, 'bulkAction'])->name('bulk-action');
    });

    // Notification Preferences
    Route::prefix('notification-preferences')->name('notification-preferences.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\NotificationPreferenceController::class, 'index'])->name('index');
        Route::post('/update', [App\Http\Controllers\Owner\NotificationPreferenceController::class, 'update'])->name('update');
        Route::post('/reset', [App\Http\Controllers\Owner\NotificationPreferenceController::class, 'reset'])->name('reset');
        Route::post('/global-settings', [App\Http\Controllers\Owner\NotificationPreferenceController::class, 'updateGlobalSettings'])->name('global-settings');
        Route::post('/test', [App\Http\Controllers\Owner\NotificationPreferenceController::class, 'testNotification'])->name('test');
    });

    // Communication (placeholder routes)
    Route::get('/email-templates', function () { return view('owner.email-templates.index'); })->name('email-templates.index');
    Route::get('/sms-settings', function () { return view('owner.sms-settings.index'); })->name('sms-settings.index');

    // Reports & Analytics
    Route::get('/analytics', function () { return view('owner.analytics.index'); })->name('analytics.index');
    Route::get('/reports/bookings', function () { return view('owner.reports.bookings'); })->name('reports.bookings');
    Route::get('/reports/revenue', function () { return view('owner.reports.revenue'); })->name('reports.revenue');
    Route::get('/reports/customers', function () { return view('owner.reports.customers'); })->name('reports.customers');
    Route::get('/reports/customers/data', [App\Http\Controllers\Owner\CustomerController::class, 'getReportData'])->name('reports.customers.data');
    Route::get('/reports/customers/export', [App\Http\Controllers\Owner\CustomerController::class, 'exportReport'])->name('reports.customers.export');
    Route::get('/reports/resources', function () { return view('owner.reports.resources'); })->name('reports.resources');

    // Financial Management - Complete Payment, Revenue & Pricing System
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\PaymentController::class, 'index'])->name('index');
        Route::get('/gateways', [App\Http\Controllers\Owner\PaymentController::class, 'gateways'])->name('gateways');
        Route::post('/settings', [App\Http\Controllers\Owner\PaymentController::class, 'updateSettings'])->name('settings');
        Route::get('/analytics', [App\Http\Controllers\Owner\PaymentController::class, 'getAnalytics'])->name('analytics');
        Route::get('/{transaction}', [App\Http\Controllers\Owner\PaymentController::class, 'showTransaction'])->name('show');
        Route::get('/{transaction}/receipt.pdf', [App\Http\Controllers\Owner\PaymentController::class, 'downloadReceipt'])->name('receipt');
        Route::post('/manual', [App\Http\Controllers\Owner\PaymentController::class, 'processManualPayment'])->name('manual');
        Route::post('/{transaction}/refund', [App\Http\Controllers\Owner\PaymentController::class, 'processRefund'])->name('refund');
        Route::post('/stripe/connect', [App\Http\Controllers\Owner\PaymentController::class, 'connectStripe'])->name('stripe.connect');
        Route::post('/paypal/connect', [App\Http\Controllers\Owner\PaymentController::class, 'connectPaypal'])->name('paypal.connect');
        Route::post('/stripe/disconnect', [App\Http\Controllers\Owner\PaymentController::class, 'disconnectStripe'])->name('stripe.disconnect');
        Route::post('/paypal/disconnect', [App\Http\Controllers\Owner\PaymentController::class, 'disconnectPaypal'])->name('paypal.disconnect');
        Route::post('/gateways/{account}/toggle', [App\Http\Controllers\Owner\PaymentController::class, 'toggleGatewayAccount'])->name('gateways.toggle');
    });

    Route::prefix('revenue')->name('revenue.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\RevenueController::class, 'index'])->name('index');
        Route::get('/analytics', [App\Http\Controllers\Owner\RevenueController::class, 'analytics'])->name('analytics');
        Route::get('/service-performance', [App\Http\Controllers\Owner\RevenueController::class, 'servicePerformance'])->name('service-performance');
        Route::get('/customer-insights', [App\Http\Controllers\Owner\RevenueController::class, 'customerInsights'])->name('customer-insights');
        Route::get('/data', [App\Http\Controllers\Owner\RevenueController::class, 'getRevenueData'])->name('data');
        Route::get('/export', [App\Http\Controllers\Owner\RevenueController::class, 'export'])->name('export');
    });

    Route::prefix('pricing')->name('pricing.')->group(function () {
        Route::get('/', [App\Http\Controllers\Owner\PricingController::class, 'index'])->name('index');
        Route::get('/strategies', [App\Http\Controllers\Owner\PricingController::class, 'strategies'])->name('strategies');
        Route::post('/strategies', [App\Http\Controllers\Owner\PricingController::class, 'storeStrategy'])->name('strategies.store');
        Route::put('/strategies/{strategy}', [App\Http\Controllers\Owner\PricingController::class, 'updateStrategy'])->name('strategies.update');
        Route::delete('/strategies/{strategy}', [App\Http\Controllers\Owner\PricingController::class, 'destroyStrategy'])->name('strategies.destroy');
        Route::get('/experiments', [App\Http\Controllers\Owner\PricingController::class, 'experiments'])->name('experiments');
        Route::post('/experiments', [App\Http\Controllers\Owner\PricingController::class, 'storeExperiment'])->name('experiments.store');
        Route::put('/experiments/{experiment}', [App\Http\Controllers\Owner\PricingController::class, 'updateExperiment'])->name('experiments.update');
        Route::delete('/experiments/{experiment}', [App\Http\Controllers\Owner\PricingController::class, 'destroyExperiment'])->name('experiments.destroy');
        Route::get('/calculator', [App\Http\Controllers\Owner\PricingController::class, 'calculator'])->name('calculator');
        Route::post('/calculate', [App\Http\Controllers\Owner\PricingController::class, 'calculatePrice'])->name('calculate');
    });

    // Account (placeholder routes)
    Route::get('/profile', function () { return view('owner.profile.index'); })->name('profile.index');
    Route::get('/security', function () { return view('owner.security.index'); })->name('security.index');
    Route::get('/subscription', function () { return view('owner.subscription.index'); })->name('subscription.index');
});

Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->middleware(['auth'])->name('dashboard'); // 'verified' temporarily disabled for development

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';

// Public Landing Page Routes
Route::get('/{slug}', [App\Http\Controllers\LandingPageController::class, 'show'])
    ->where('slug', '^(?!admin|api|owner|customer|dashboard|login|register|password)[a-z0-9-]+$')
    ->name('landing-page.show');

Route::get('/{slug}/services', [App\Http\Controllers\LandingPageController::class, 'services'])
    ->where('slug', '^(?!admin|api|owner|customer|dashboard|login|register|password)[a-z0-9-]+$')
    ->name('landing-page.services');

Route::get('/{slug}/booking', [App\Http\Controllers\LandingPageController::class, 'booking'])
    ->where('slug', '^(?!admin|api|owner|customer|dashboard|login|register|password)[a-z0-9-]+$')
    ->name('landing-page.booking');

Route::post('/{slug}/contact', [App\Http\Controllers\LandingPageController::class, 'contact'])
    ->where('slug', '^(?!admin|api|owner|customer|dashboard|login|register|password)[a-z0-9-]+$')
    ->name('landing-page.contact');

Route::get('/{slug}/sitemap.xml', [App\Http\Controllers\LandingPageController::class, 'sitemap'])
    ->where('slug', '^(?!admin|api|owner|customer|dashboard|login|register|password)[a-z0-9-]+$')
    ->name('landing-page.sitemap');

// Subdomain routing (for premium plans)
Route::domain('{subdomain}.bookkei.com')->group(function () {
    Route::get('/', [App\Http\Controllers\LandingPageController::class, 'subdomain'])->name('landing-page.subdomain');
    Route::get('/services', [App\Http\Controllers\LandingPageController::class, 'services'])->name('landing-page.subdomain.services');
    Route::get('/booking', [App\Http\Controllers\LandingPageController::class, 'booking'])->name('landing-page.subdomain.booking');
    Route::post('/contact', [App\Http\Controllers\LandingPageController::class, 'contact'])->name('landing-page.subdomain.contact');
});

// Customer routes
Route::prefix('customer')->name('customer.')->middleware(['auth'])->group(function () { // 'verified' temporarily disabled for development
    // Dashboard
    Route::get('/dashboard', [App\Http\Controllers\Customer\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/quick-stats', [App\Http\Controllers\Customer\DashboardController::class, 'getQuickStats'])->name('dashboard.quick-stats');



    // Booking Management
    Route::resource('bookings', App\Http\Controllers\Customer\BookingController::class);
    Route::post('/bookings/{booking}/cancel', [App\Http\Controllers\Customer\BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::get('/bookings/history/view', [App\Http\Controllers\Customer\BookingController::class, 'history'])->name('bookings.history');
    Route::get('/bookings/available-slots', [App\Http\Controllers\Customer\BookingController::class, 'getAvailableSlots'])->name('bookings.available-slots');

    // Services & Browsing
    Route::get('/services', [App\Http\Controllers\Customer\ServiceController::class, 'index'])->name('services.index');
    Route::get('/services/categories', [App\Http\Controllers\Customer\ServiceController::class, 'categories'])->name('services.categories');
    Route::get('/services/search', [App\Http\Controllers\Customer\ServiceController::class, 'search'])->name('services.search');
    Route::get('/services/{service}', [App\Http\Controllers\Customer\ServiceController::class, 'show'])->name('services.show');
    Route::get('/services/{service}/available-slots', [App\Http\Controllers\Customer\ServiceController::class, 'getAvailableSlots'])->name('services.available-slots');
    Route::get('/services/{service}/check-availability', [App\Http\Controllers\Customer\ServiceController::class, 'checkAvailability'])->name('services.check-availability');

    // Favorites
    Route::get('/favorites', [App\Http\Controllers\Customer\FavoriteController::class, 'index'])->name('favorites.index');
    Route::post('/favorites/toggle', [App\Http\Controllers\Customer\FavoriteController::class, 'toggle'])->name('favorites.toggle');
    Route::post('/favorites', [App\Http\Controllers\Customer\FavoriteController::class, 'store'])->name('favorites.store');
    Route::delete('/favorites', [App\Http\Controllers\Customer\FavoriteController::class, 'destroy'])->name('favorites.destroy');
    Route::get('/favorites/check', [App\Http\Controllers\Customer\FavoriteController::class, 'check'])->name('favorites.check');
    Route::get('/favorites/count', [App\Http\Controllers\Customer\FavoriteController::class, 'count'])->name('favorites.count');

    // Profile Management
    Route::get('/profile', [App\Http\Controllers\Customer\ProfileController::class, 'index'])->name('profile.index');
    Route::get('/profile/edit', [App\Http\Controllers\Customer\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [App\Http\Controllers\Customer\ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/password', [App\Http\Controllers\Customer\ProfileController::class, 'showPasswordForm'])->name('profile.password');
    Route::put('/profile/password', [App\Http\Controllers\Customer\ProfileController::class, 'updatePassword'])->name('profile.update-password');
    Route::get('/preferences', [App\Http\Controllers\Customer\ProfileController::class, 'preferences'])->name('preferences');
    Route::put('/preferences', [App\Http\Controllers\Customer\ProfileController::class, 'updatePreferences'])->name('preferences.update');
    Route::get('/privacy', [App\Http\Controllers\Customer\ProfileController::class, 'privacy'])->name('privacy');
    Route::put('/privacy', [App\Http\Controllers\Customer\ProfileController::class, 'updatePrivacy'])->name('privacy.update');
    Route::get('/profile/export', [App\Http\Controllers\Customer\ProfileController::class, 'exportData'])->name('profile.export');
    Route::delete('/profile', [App\Http\Controllers\Customer\ProfileController::class, 'deleteAccount'])->name('profile.delete');

    // Reviews & Ratings
    Route::get('/reviews', function () { return view('customer.reviews.index'); })->name('reviews.index');
    Route::get('/reviews/create/{booking}', function () { return view('customer.reviews.create'); })->name('reviews.create');
    Route::post('/reviews', function () { return redirect()->route('customer.reviews.index'); })->name('reviews.store');

    // Payment Methods
    Route::get('/payment-methods', function () { return view('customer.payment-methods.index'); })->name('payment-methods.index');
    Route::post('/payment-methods', function () { return redirect()->route('customer.payment-methods.index'); })->name('payment-methods.store');
    Route::delete('/payment-methods/{paymentMethod}', function () { return redirect()->route('customer.payment-methods.index'); })->name('payment-methods.destroy');

    // Billing History
    Route::get('/billing', function () { return view('customer.billing.index'); })->name('billing.index');
    Route::get('/billing/{invoice}/download', function () { return response()->download('invoice.pdf'); })->name('billing.download');

    // Messages & Communication
    Route::get('/messages', function () { return view('customer.messages.index'); })->name('messages.index');
    Route::get('/messages/{conversation}', function () { return view('customer.messages.show'); })->name('messages.show');
    Route::post('/messages/{conversation}', function () { return redirect()->back(); })->name('messages.send');

    // Notifications
    Route::get('/notifications', function () { return view('customer.notifications.index'); })->name('notifications.index');
    Route::post('/notifications/{notification}/read', function () { return response()->json(['success' => true]); })->name('notifications.read');
    Route::post('/notifications/mark-all-read', function () { return response()->json(['success' => true]); })->name('notifications.mark-all-read');

    // Support Center
    Route::get('/support', function () { return view('customer.support.index'); })->name('support.index');
    Route::get('/support/contact', function () { return view('customer.support.contact'); })->name('support.contact');
    Route::post('/support/contact', function () { return redirect()->route('customer.support.index'); })->name('support.contact.send');

    // Activity Log
    Route::get('/activity', function () { return view('customer.activity.index'); })->name('activity.index');

    // Loyalty Points
    Route::get('/loyalty', function () { return view('customer.loyalty.index'); })->name('loyalty.index');
    Route::get('/loyalty/redeem/{reward}', function () { return view('customer.loyalty.redeem'); })->name('loyalty.redeem');

    // Referrals
    Route::get('/referrals', function () { return view('customer.referrals.index'); })->name('referrals.index');
    Route::post('/referrals/send', function () { return redirect()->route('customer.referrals.index'); })->name('referrals.send');

    // Help Center
    Route::get('/help', function () { return view('customer.help.index'); })->name('help.index');
    Route::get('/help/{article}', function () { return view('customer.help.article'); })->name('help.article');

    // Contact Us
    Route::get('/contact', function () { return view('customer.contact.index'); })->name('contact.index');
    Route::post('/contact', function () { return redirect()->route('customer.contact.index'); })->name('contact.send');

    // Legal Pages
    Route::get('/legal', function () { return view('customer.legal.index'); })->name('legal.index');
    Route::get('/legal/terms', function () { return view('customer.legal.terms'); })->name('legal.terms');
    Route::get('/legal/privacy', function () { return view('customer.legal.privacy'); })->name('legal.privacy');
});

// Include test routes in development
if (app()->environment(['local', 'testing'])) {
    require __DIR__.'/test.php';

    // Debug route for operating hours
    Route::get('/debug/operating-hours', function () {
        $user = Auth::user();
        if (!$user) {
            return 'Not authenticated';
        }

        $business = $user->ownedBusinesses()->active()->first();
        if (!$business) {
            return 'No business found';
        }

        $hours = $business->operatingHours()->get();

        return [
            'business_id' => $business->id,
            'business_name' => $business->name,
            'hours_count' => $hours->count(),
            'hours' => $hours->toArray()
        ];
    })->middleware('auth');



}
