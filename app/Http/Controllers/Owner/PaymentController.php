<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Business;
use App\Models\PaymentTransaction;
use App\Models\PaymentGatewayAccount;
use App\Models\BusinessFinancialConfig;
use App\Models\RevenueRecord;
use App\Models\Booking;
use App\Models\Service;
use App\Models\User;
use Carbon\Carbon;

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'financial.isolation']);
    }

    /**
     * Display the payment management dashboard
     */
    public function index(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);

        // Get payment gateway accounts
        $gatewayAccounts = PaymentGatewayAccount::where('business_id', $businessId)
                                              ->with('transactions')
                                              ->get();

        // Get recent transactions
        $recentTransactions = PaymentTransaction::where('business_id', $businessId)
                                               ->with(['booking', 'customer', 'service', 'gatewayAccount'])
                                               ->when($dateRange, function($query) use ($dateRange) {
                                                   $query->whereBetween('processed_at', $dateRange);
                                               })
                                               ->orderBy('processed_at', 'desc')
                                               ->limit(50)
                                               ->get();

        // Get payment statistics
        $paymentStats = $this->getPaymentStatistics($businessId, $dateRange);

        // Get gateway performance metrics
        $gatewayMetrics = $this->getGatewayMetrics($businessId, $dateRange);

        // Get failed transactions for monitoring
        $failedTransactions = PaymentTransaction::where('business_id', $businessId)
                                               ->where('status', 'failed')
                                               ->whereBetween('created_at', [now()->subDays(7), now()])
                                               ->count();

        return view('owner.payments.index', compact(
            'gatewayAccounts',
            'recentTransactions',
            'paymentStats',
            'gatewayMetrics',
            'failedTransactions',
            'dateRange'
        ));
    }

    /**
     * Show gateway setup page
     */
    public function gateways()
    {
        $businessId = $this->getCurrentBusinessId();

        $stripeAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                            ->where('gateway_type', 'stripe')
                                            ->first();

        $paypalAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                            ->where('gateway_type', 'paypal')
                                            ->first();

        return view('owner.payments.gateways', compact('stripeAccount', 'paypalAccount'));
    }

    /**
     * Show transaction details
     */
    public function showTransaction($transactionId)
    {
        $businessId = $this->getCurrentBusinessId();

        $transaction = PaymentTransaction::where('business_id', $businessId)
                                         ->where('id', $transactionId)
                                         ->with(['booking', 'customer', 'service', 'gatewayAccount'])
                                         ->firstOrFail();

        // Get related transactions (refunds, etc.)
        $relatedTransactions = PaymentTransaction::where('business_id', $businessId)
                                                ->where('booking_id', $transaction->booking_id)
                                                ->where('id', '!=', $transaction->id)
                                                ->get();

        return view('owner.payments.show', compact('transaction', 'relatedTransactions'));
    }

    /**
     * Process a manual payment
     */
    public function processManualPayment(Request $request)
    {
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,card,bank_transfer,other',
            'description' => 'nullable|string|max:500',
        ]);

        $businessId = $this->getCurrentBusinessId();

        // Verify booking belongs to this business
        $booking = Booking::where('id', $request->booking_id)
                         ->where('business_id', $businessId)
                         ->firstOrFail();

        // Create manual payment transaction
        $transaction = PaymentTransaction::create([
            'business_id' => $businessId,
            'owner_id' => Auth::id(),
            'booking_id' => $booking->id,
            'customer_id' => $booking->customer_id,
            'service_id' => $booking->bookingServices->first()->service_id ?? null,
            'gateway_account_id' => 1, // Default for manual transactions
            'gateway_type' => 'manual',
            'transaction_type' => 'payment',
            'payment_method' => $request->payment_method,
            'gross_amount' => $request->amount,
            'gateway_fees' => 0,
            'platform_fees' => 0,
            'net_amount' => $request->amount,
            'currency' => 'USD',
            'status' => 'completed',
            'description' => $request->description,
            'processed_at' => now(),
        ]);

        // Create revenue record
        $this->createRevenueRecord($transaction);

        // Update booking payment status
        $this->updateBookingPaymentStatus($booking);

        return redirect()->route('owner.payments.index')
                        ->with('success', 'Manual payment processed successfully.');
    }

    /**
     * Process a refund
     */
    public function processRefund(Request $request, $transactionId)
    {
        $request->validate([
            'refund_amount' => 'required|numeric|min:0.01',
            'reason' => 'required|string|max:500',
        ]);

        $businessId = $this->getCurrentBusinessId();

        $originalTransaction = PaymentTransaction::where('business_id', $businessId)
                                                ->where('id', $transactionId)
                                                ->where('status', 'completed')
                                                ->firstOrFail();

        // Validate refund amount
        if ($request->refund_amount > $originalTransaction->net_amount) {
            return back()->withErrors(['refund_amount' => 'Refund amount cannot exceed original payment amount.']);
        }

        // Create refund transaction
        $refundTransaction = PaymentTransaction::create([
            'business_id' => $businessId,
            'owner_id' => Auth::id(),
            'booking_id' => $originalTransaction->booking_id,
            'customer_id' => $originalTransaction->customer_id,
            'service_id' => $originalTransaction->service_id,
            'gateway_account_id' => $originalTransaction->gateway_account_id,
            'gateway_type' => $originalTransaction->gateway_type,
            'transaction_type' => $request->refund_amount == $originalTransaction->net_amount ? 'refund' : 'partial_refund',
            'payment_method' => $originalTransaction->payment_method,
            'gross_amount' => -$request->refund_amount,
            'gateway_fees' => 0,
            'platform_fees' => 0,
            'net_amount' => -$request->refund_amount,
            'currency' => $originalTransaction->currency,
            'status' => 'completed',
            'description' => $request->reason,
            'processed_at' => now(),
            'metadata' => ['original_transaction_id' => $originalTransaction->id],
        ]);

        // Update original transaction status
        $originalTransaction->update([
            'status' => $request->refund_amount == $originalTransaction->net_amount ? 'refunded' : 'partially_refunded'
        ]);

        // Create negative revenue record for refund
        $this->createRevenueRecord($refundTransaction);

        return redirect()->route('owner.payments.show', $transactionId)
                        ->with('success', 'Refund processed successfully.');
    }

    /**
     * Connect Stripe account
     */
    public function connectStripe(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        // Implementation would involve Stripe Connect OAuth flow
        // For now, this is a placeholder

        return redirect()->route('owner.payments.gateways')
                        ->with('info', 'Stripe Connect integration will be implemented here.');
    }

    /**
     * Connect PayPal account
     */
    public function connectPaypal(Request $request)
    {
        // PayPal Commerce Platform integration
        // This would handle the PayPal Connect flow
        return response()->json(['success' => true, 'message' => 'PayPal Connect initiated']);
    }

    /**
     * Update payment settings
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'default_currency' => 'required|string|size:3',
            'auto_capture' => 'boolean',
            'send_receipts' => 'boolean',
            'require_billing_address' => 'boolean',
            'payment_terms' => 'nullable|string|max:1000',
        ]);

        $businessId = $this->getCurrentBusinessId();

        // Update or create business financial configuration
        BusinessFinancialConfig::updateOrCreate(
            ['business_id' => $businessId],
            [
                'default_currency' => $request->default_currency,
                'auto_capture_payments' => $request->boolean('auto_capture', true),
                'send_payment_receipts' => $request->boolean('send_receipts', true),
                'require_billing_address' => $request->boolean('require_billing_address', false),
                'payment_terms' => $request->payment_terms,
                'updated_at' => now(),
            ]
        );

        return redirect()->route('owner.payments.gateways')
                        ->with('success', 'Payment settings updated successfully.');
    }

    /**
     * Disconnect Stripe account
     */
    public function disconnectStripe(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        $stripeAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                            ->where('gateway_type', 'stripe')
                                            ->first();

        if ($stripeAccount) {
            $stripeAccount->update([
                'is_active' => false,
                'status' => 'disconnected',
                'disconnected_at' => now(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Stripe account disconnected successfully'
        ]);
    }

    /**
     * Disconnect PayPal account
     */
    public function disconnectPaypal(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();

        $paypalAccount = PaymentGatewayAccount::where('business_id', $businessId)
                                            ->where('gateway_type', 'paypal')
                                            ->first();

        if ($paypalAccount) {
            $paypalAccount->update([
                'is_active' => false,
                'status' => 'disconnected',
                'disconnected_at' => now(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'PayPal account disconnected successfully'
        ]);
    }

    /**
     * Toggle gateway account status
     */
    public function toggleGatewayAccount(Request $request, $accountId)
    {
        $businessId = $this->getCurrentBusinessId();

        $account = PaymentGatewayAccount::where('business_id', $businessId)
                                      ->where('id', $accountId)
                                      ->firstOrFail();

        $account->update([
            'is_active' => !$account->is_active,
            'status' => !$account->is_active ? 'active' : 'inactive',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Gateway account status updated successfully',
            'is_active' => $account->is_active
        ]);
    }

    /**
     * Download payment receipt as PDF
     */
    public function downloadReceipt($transactionId)
    {
        $businessId = $this->getCurrentBusinessId();

        $transaction = PaymentTransaction::where('business_id', $businessId)
                                         ->where('id', $transactionId)
                                         ->with(['booking', 'customer', 'service', 'gatewayAccount'])
                                         ->firstOrFail();

        // For now, return a simple response - in production this would generate a PDF
        return response()->json([
            'message' => 'PDF generation would be implemented here',
            'transaction' => $transaction->transaction_id
        ]);
    }

    /**
     * Get payment analytics data for charts
     */
    public function getAnalytics(Request $request)
    {
        $businessId = $this->getCurrentBusinessId();
        $dateRange = $this->getDateRange($request);
        $groupBy = $request->get('group_by', 'day');

        $query = PaymentTransaction::where('business_id', $businessId)
                                  ->where('status', 'completed')
                                  ->when($dateRange, function($q) use ($dateRange) {
                                      $q->whereBetween('processed_at', $dateRange);
                                  });

        // Group by time period
        switch ($groupBy) {
            case 'hour':
                $data = $query->selectRaw('DATE_FORMAT(processed_at, "%Y-%m-%d %H:00:00") as period,
                                         SUM(net_amount) as revenue,
                                         COUNT(*) as transactions')
                             ->groupBy('period')
                             ->orderBy('period')
                             ->get();
                break;
            case 'day':
                $data = $query->selectRaw('DATE(processed_at) as period,
                                         SUM(net_amount) as revenue,
                                         COUNT(*) as transactions')
                             ->groupBy('period')
                             ->orderBy('period')
                             ->get();
                break;
            case 'month':
                $data = $query->selectRaw('DATE_FORMAT(processed_at, "%Y-%m") as period,
                                         SUM(net_amount) as revenue,
                                         COUNT(*) as transactions')
                             ->groupBy('period')
                             ->orderBy('period')
                             ->get();
                break;
        }

        return response()->json($data);
    }

    /**
     * Get current business ID
     */
    private function getCurrentBusinessId()
    {
        return session('current_business_id') ??
               Business::where('owner_id', Auth::id())->first()->id ??
               abort(404, 'No business found');
    }

    /**
     * Get date range from request
     */
    private function getDateRange(Request $request)
    {
        $period = $request->get('period', 'month');

        switch ($period) {
            case 'today':
                return [now()->startOfDay(), now()->endOfDay()];
            case 'yesterday':
                return [now()->yesterday()->startOfDay(), now()->yesterday()->endOfDay()];
            case 'week':
                return [now()->startOfWeek(), now()->endOfWeek()];
            case 'month':
                return [now()->startOfMonth(), now()->endOfMonth()];
            case 'quarter':
                return [now()->startOfQuarter(), now()->endOfQuarter()];
            case 'year':
                return [now()->startOfYear(), now()->endOfYear()];
            case 'custom':
                if ($request->has(['start_date', 'end_date'])) {
                    return [
                        Carbon::parse($request->start_date)->startOfDay(),
                        Carbon::parse($request->end_date)->endOfDay()
                    ];
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * Get payment statistics
     */
    private function getPaymentStatistics($businessId, $dateRange = null)
    {
        $query = PaymentTransaction::where('business_id', $businessId);

        if ($dateRange) {
            $query->whereBetween('processed_at', $dateRange);
        }

        $stats = [
            'total_transactions' => $query->count(),
            'successful_transactions' => $query->where('status', 'completed')->count(),
            'failed_transactions' => $query->where('status', 'failed')->count(),
            'pending_transactions' => $query->where('status', 'pending')->count(),
            'total_volume' => $query->where('status', 'completed')->sum('gross_amount'),
            'total_fees' => $query->where('status', 'completed')->sum('gateway_fees'),
            'net_revenue' => $query->where('status', 'completed')->sum('net_amount'),
            'average_transaction' => 0,
            'success_rate' => 0,
        ];

        if ($stats['total_transactions'] > 0) {
            $stats['average_transaction'] = $stats['total_volume'] / $stats['successful_transactions'];
            $stats['success_rate'] = ($stats['successful_transactions'] / $stats['total_transactions']) * 100;
        }

        return $stats;
    }

    /**
     * Get gateway performance metrics
     */
    private function getGatewayMetrics($businessId, $dateRange = null)
    {
        $query = PaymentTransaction::where('business_id', $businessId);

        if ($dateRange) {
            $query->whereBetween('processed_at', $dateRange);
        }

        return $query->selectRaw('
                        gateway_type,
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful_transactions,
                        SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_transactions,
                        SUM(CASE WHEN status = "completed" THEN gross_amount ELSE 0 END) as total_volume,
                        SUM(CASE WHEN status = "completed" THEN gateway_fees ELSE 0 END) as total_fees,
                        AVG(CASE WHEN status = "completed" THEN gross_amount ELSE NULL END) as avg_transaction
                    ')
                    ->groupBy('gateway_type')
                    ->get()
                    ->keyBy('gateway_type');
    }

    /**
     * Create revenue record from transaction
     */
    private function createRevenueRecord(PaymentTransaction $transaction)
    {
        RevenueRecord::create([
            'business_id' => $transaction->business_id,
            'owner_id' => $transaction->owner_id,
            'transaction_id' => $transaction->id,
            'gateway_type' => $transaction->gateway_type,
            'service_id' => $transaction->service_id,
            'customer_id' => $transaction->customer_id,
            'booking_id' => $transaction->booking_id,
            'gross_amount' => $transaction->gross_amount,
            'gateway_fees' => $transaction->gateway_fees,
            'platform_fees' => $transaction->platform_fees,
            'net_amount' => $transaction->net_amount,
            'tax_amount' => $transaction->tax_amount,
            'currency' => $transaction->currency,
            'revenue_date' => $transaction->processed_at->toDateString(),
            'revenue_category' => 'service',
            'payment_method' => $transaction->payment_method,
            'status' => $transaction->status === 'completed' ? 'completed' : 'pending',
        ]);
    }

    /**
     * Update booking payment status based on payments
     */
    private function updateBookingPaymentStatus(Booking $booking)
    {
        $totalPaid = PaymentTransaction::where('booking_id', $booking->id)
                                      ->where('status', 'completed')
                                      ->sum('net_amount');

        if ($totalPaid >= $booking->total_amount) {
            $booking->update(['payment_status' => 'paid']);
        } elseif ($totalPaid > 0) {
            $booking->update(['payment_status' => 'partial']);
        }
    }
}
