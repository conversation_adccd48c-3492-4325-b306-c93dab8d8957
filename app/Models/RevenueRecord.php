<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class RevenueRecord extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'owner_id',
        'transaction_id',
        'gateway_type',
        'service_id',
        'customer_id',
        'booking_id',
        'gross_amount',
        'gateway_fees',
        'platform_fees',
        'net_amount',
        'tax_amount',
        'currency',
        'revenue_date',
        'revenue_category',
        'payment_method',
        'refund_amount',
        'status',
        'metadata',
    ];

    protected $casts = [
        'gross_amount' => 'decimal:2',
        'gateway_fees' => 'decimal:2',
        'platform_fees' => 'decimal:2',
        'net_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'revenue_date' => 'date',
        'metadata' => 'array',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(PaymentTransaction::class, 'transaction_id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    // Scopes
    public function scopeBusinessOwned($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('revenue_date', [$startDate, $endDate]);
    }

    public function scopeByService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }

    public function scopeByCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('revenue_category', $category);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('revenue_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('revenue_date', now()->month)
                    ->whereYear('revenue_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('revenue_date', now()->year);
    }

    // Accessors
    public function getFormattedAmountAttribute()
    {
        return number_format($this->gross_amount, 2);
    }

    public function getFormattedNetAmountAttribute()
    {
        return number_format($this->net_amount, 2);
    }

    public function getProfitMarginAttribute()
    {
        if ($this->gross_amount == 0) return 0;
        return (($this->net_amount / $this->gross_amount) * 100);
    }

    public function getTotalFeesAttribute()
    {
        return $this->gateway_fees + $this->platform_fees;
    }

    // Static Analytics Methods
    public static function getTotalRevenue($businessId, $dateRange = null)
    {
        $query = static::where('business_id', $businessId)->where('status', 'completed');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        return $query->sum('net_amount');
    }

    public static function getRevenueByMonth($businessId, $year = null)
    {
        $year = $year ?? date('Y');

        return static::where('business_id', $businessId)
                    ->where('status', 'completed')
                    ->whereYear('revenue_date', $year)
                    ->selectRaw('MONTH(revenue_date) as month, SUM(net_amount) as total')
                    ->groupBy('month')
                    ->orderBy('month')
                    ->get()
                    ->keyBy('month');
    }

    public static function getRevenueByService($businessId, $dateRange = null)
    {
        $query = static::where('business_id', $businessId)
                      ->where('status', 'completed')
                      ->with('service:id,name');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        return $query->selectRaw('service_id, SUM(net_amount) as total_revenue, COUNT(*) as transaction_count')
                    ->groupBy('service_id')
                    ->orderByDesc('total_revenue')
                    ->get();
    }

    public static function getRevenueByCustomer($businessId, $dateRange = null, $limit = 10)
    {
        $query = static::where('business_id', $businessId)
                      ->where('status', 'completed')
                      ->with('customer:id,name,email');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        return $query->selectRaw('customer_id, SUM(net_amount) as total_spent, COUNT(*) as visit_count')
                    ->groupBy('customer_id')
                    ->orderByDesc('total_spent')
                    ->limit($limit)
                    ->get();
    }

    public static function getRevenueGrowth($businessId, $periods = 12)
    {
        $results = static::where('business_id', $businessId)
                        ->where('status', 'completed')
                        ->where('revenue_date', '>=', now()->subMonths($periods))
                        ->selectRaw('
                            DATE_FORMAT(revenue_date, "%Y-%m") as period,
                            SUM(net_amount) as revenue,
                            COUNT(*) as transactions
                        ')
                        ->groupBy('period')
                        ->orderBy('period')
                        ->get();

        // Calculate growth rates
        $growthData = [];
        $previousRevenue = null;

        foreach ($results as $index => $result) {
            $growthRate = 0;
            if ($previousRevenue !== null && $previousRevenue > 0) {
                $growthRate = (($result->revenue - $previousRevenue) / $previousRevenue) * 100;
            }

            $growthData[] = [
                'period' => $result->period,
                'revenue' => $result->revenue,
                'transactions' => $result->transactions,
                'growth_rate' => round($growthRate, 2)
            ];

            $previousRevenue = $result->revenue;
        }

        return $growthData;
    }

    public static function getRevenueMetrics($businessId, $dateRange = null)
    {
        $query = static::where('business_id', $businessId)->where('status', 'completed');

        if ($dateRange) {
            $query->whereBetween('revenue_date', $dateRange);
        }

        $totalRevenue = $query->sum('net_amount');
        $totalTransactions = $query->count();
        $totalFees = $query->sum('gateway_fees') + $query->sum('platform_fees');
        $avgTransactionValue = $totalTransactions > 0 ? $totalRevenue / $totalTransactions : 0;

        // Get unique customers
        $uniqueCustomers = $query->distinct('customer_id')->count('customer_id');

        // Get refund data
        $totalRefunds = static::where('business_id', $businessId)
                             ->where('status', 'refunded')
                             ->when($dateRange, function($q) use ($dateRange) {
                                 $q->whereBetween('revenue_date', $dateRange);
                             })
                             ->sum('refund_amount');

        return [
            'total_revenue' => $totalRevenue,
            'total_transactions' => $totalTransactions,
            'average_transaction_value' => round($avgTransactionValue, 2),
            'total_fees' => $totalFees,
            'unique_customers' => $uniqueCustomers,
            'total_refunds' => $totalRefunds,
            'refund_rate' => $totalRevenue > 0 ? round(($totalRefunds / $totalRevenue) * 100, 2) : 0,
        ];
    }

    public static function getDailyRevenue($businessId, $days = 30)
    {
        return static::where('business_id', $businessId)
                    ->where('status', 'completed')
                    ->where('revenue_date', '>=', now()->subDays($days))
                    ->selectRaw('revenue_date, SUM(net_amount) as daily_revenue')
                    ->groupBy('revenue_date')
                    ->orderBy('revenue_date')
                    ->get();
    }
}
